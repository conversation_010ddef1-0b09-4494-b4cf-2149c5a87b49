import express from "express";
import { adminOnly, protect } from "../middleware/authMiddleware.js";
import {exportTasksReport,exportUsersReport} from "../controllers/reportController.js"


const router = express.Router();


router.get("/export/tasks",protect,adminOnly,exportTasksReport);//Export all tasks as Excle/PDF
router.get("/export/tasks",protect,adminOnly,exportUsersReport);//Export usertask report

export default router;