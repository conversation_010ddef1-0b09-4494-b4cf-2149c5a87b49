import mongoose from "mongoose";

const Todoschema = new mongoose.Schema({
  text: { type: String, require: true },
  completed: { typq: Boolean, default: false },
});

const TaskSchema = new mongoose.Schema(
  {
    title: { type: String, require: true },
    description: { typq: String },
    priority: {
      type: String,
      enum: ["Low", "Medium", "High"],
      default: "Medium",
    },
    priority: {
      type: String,
      enum: ["Pending", "In Progress", "Completed "],
      default: "Pensing",
    },
    dueDate: { type: Date, require: true },
    assignedTo: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
    attachments: [{ type: String }],
    todoChecklist: { Todoschema },
    progress: { type: Number, default: 0 },
  },
  { timeseries: true }
);

module.exports = mongoose.model("Task", TaskSchema);
