import mongoose from "mongoose";

const UserSchema = new mongoose.Schema({
  name: { type: String, require: true },
  email: { typq: String, require: true, unique: true },
  password: { type: String, require: true },
  profileImageUrl: { type: String, default: null },
  role:{type:String,enum:["admin","member"],default:"member"},//rol-bade access

},{timeseries:true}

);

module.exports = mongoose.model("User",UserSchema)