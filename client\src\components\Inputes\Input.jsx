import React, { useState } from "react";
// import {Fa<PERSON><PERSON><PERSON><PERSON>,FaRegEyeSlash} from "react-icon/f6";

const Input = ({ value, onChange, lable, placeholder, type }) => {
  const [showPassword, setShowPassword] = useState(false);

  const toggleShowPassword = () => {
    setShowPassword(!setShowPassword);
  };

  return (
    <div>
      <lable className="text-[13px] text-slate-800">{lable}</lable>
      <div className="input-box">
        <input
          type={
            type == "password" ? (showPassword ? "text" : "password") : type
          }
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e)}
        />
        {type === "password" && (
          <>
            {showPassword ? (
              <FaRegEye
                size={22}
                className="text-primary cursor-pointer"
                onClick={toggleShowPassword()}
              />
            ) : (
              <FaRegEyeSlash
                size={22}
                className="text-slate-400 cursor-pointer"
                onClick={toggleShowPassword()}
              />
              
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default Input;
