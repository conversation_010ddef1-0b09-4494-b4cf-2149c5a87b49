import React, { useState } from "react";
import { FaReg<PERSON>ye, FaRegEyeSlash } from "react-icons/fa6";

const Input = ({ value, onChange, lable, placeholder, type }) => {
  const [showPassword, setShowPassword] = useState(false);

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div>
      <label className="text-[13px] text-slate-800">{lable}</label>
      <div className="input-box">
        <input
          type={
            type === "password" ? (showPassword ? "text" : "password") : type
          }
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          className="flex-1 bg-transparent outline-none"
        />
        {type === "password" && (
          <div className="flex items-center">
            {showPassword ? (
              <FaRegEye
                size={22}
                className="text-blue-500 cursor-pointer hover:text-blue-600"
                onClick={toggleShowPassword}
              />
            ) : (
              <FaRegEyeSlash
                size={22}
                className="text-slate-400 cursor-pointer hover:text-slate-600"
                onClick={toggleShowPassword}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Input;
