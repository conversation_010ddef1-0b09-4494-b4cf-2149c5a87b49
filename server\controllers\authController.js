import User from "../models/User.js";
import bycrypt from "bcryptjs";
import jwt from "jsonwebtoken";

//Genarate jwt token

const genarateToken =(userId)=>{
    return jwt.sign({userId},process.env.JWT_SECRET,{expiresIn:"7d"})
}

//Register user

const registerUser = async (req, res) => {}

//Login user

const loginUser = async (req, res) => {}

//Get user profile

const getUserProfile = async (req, res) => {}

//Update user profile

const updateUserProfile = async (req, res) => {}

export {registerUser,loginUser,getUserProfile,updateUserProfile}