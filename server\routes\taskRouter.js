import express from "express";
import { adminOnly, protect } from "../middleware/authMiddleware.js";
import {
  getDashboardData,
  getTaskById,
  updateTask,
  createTask,
  deleteTask,
  updateTaskChecklist,
  updateTaskStatus,
  getTasks,
  getUserDashboardData,
} from "../controllers/taskController.js";

const router = express.Router();

//Task Management Routes
router.get("/dashboard-data", protect, getDashboardData);
router.get("/user-dashboard-data", protect, getUserDashboardData);
router.get("/", protect, getTasks);
router.get("/:id", protect, getTaskById);
router.get("/", protect, adminOnly, createTask);
router.get("/:id", protect, updateTask);
router.get("/:id", protect, adminOnly, deleteTask);
router.get("/:id/status", protect, updateTaskStatus);
router.get("/:id/todo", protect, updateTaskChecklist);

export default router;
