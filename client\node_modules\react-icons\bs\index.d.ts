// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const BsFill0CircleFill: IconType;
export declare const BsFill0SquareFill: IconType;
export declare const BsFill1CircleFill: IconType;
export declare const BsFill1SquareFill: IconType;
export declare const BsFill2CircleFill: IconType;
export declare const BsFill2SquareFill: IconType;
export declare const BsFill3CircleFill: IconType;
export declare const BsFill3SquareFill: IconType;
export declare const BsFill4CircleFill: IconType;
export declare const BsFill4SquareFill: IconType;
export declare const BsFill5CircleFill: IconType;
export declare const BsFill5SquareFill: IconType;
export declare const BsFill6CircleFill: IconType;
export declare const BsFill6SquareFill: IconType;
export declare const BsFill7CircleFill: IconType;
export declare const BsFill7SquareFill: IconType;
export declare const BsFill8CircleFill: IconType;
export declare const BsFill8SquareFill: IconType;
export declare const BsFill9CircleFill: IconType;
export declare const BsFill9SquareFill: IconType;
export declare const BsFillAirplaneEnginesFill: IconType;
export declare const BsFillAirplaneFill: IconType;
export declare const BsFillAlarmFill: IconType;
export declare const BsFillArchiveFill: IconType;
export declare const BsFillArrowDownCircleFill: IconType;
export declare const BsFillArrowDownLeftCircleFill: IconType;
export declare const BsFillArrowDownLeftSquareFill: IconType;
export declare const BsFillArrowDownRightCircleFill: IconType;
export declare const BsFillArrowDownRightSquareFill: IconType;
export declare const BsFillArrowDownSquareFill: IconType;
export declare const BsFillArrowLeftCircleFill: IconType;
export declare const BsFillArrowLeftSquareFill: IconType;
export declare const BsFillArrowRightCircleFill: IconType;
export declare const BsFillArrowRightSquareFill: IconType;
export declare const BsFillArrowThroughHeartFill: IconType;
export declare const BsFillArrowUpCircleFill: IconType;
export declare const BsFillArrowUpLeftCircleFill: IconType;
export declare const BsFillArrowUpLeftSquareFill: IconType;
export declare const BsFillArrowUpRightCircleFill: IconType;
export declare const BsFillArrowUpRightSquareFill: IconType;
export declare const BsFillArrowUpSquareFill: IconType;
export declare const BsFillAspectRatioFill: IconType;
export declare const BsFillAwardFill: IconType;
export declare const BsFillBackpackFill: IconType;
export declare const BsFillBackpack2Fill: IconType;
export declare const BsFillBackpack3Fill: IconType;
export declare const BsFillBackpack4Fill: IconType;
export declare const BsFillBackspaceFill: IconType;
export declare const BsFillBackspaceReverseFill: IconType;
export declare const BsFillBadge3dFill: IconType;
export declare const BsFillBadge4kFill: IconType;
export declare const BsFillBadge8kFill: IconType;
export declare const BsFillBadgeAdFill: IconType;
export declare const BsFillBadgeArFill: IconType;
export declare const BsFillBadgeCcFill: IconType;
export declare const BsFillBadgeHdFill: IconType;
export declare const BsFillBadgeSdFill: IconType;
export declare const BsFillBadgeTmFill: IconType;
export declare const BsFillBadgeVoFill: IconType;
export declare const BsFillBadgeVrFill: IconType;
export declare const BsFillBadgeWcFill: IconType;
export declare const BsFillBagCheckFill: IconType;
export declare const BsFillBagDashFill: IconType;
export declare const BsFillBagFill: IconType;
export declare const BsFillBagHeartFill: IconType;
export declare const BsFillBagPlusFill: IconType;
export declare const BsFillBagXFill: IconType;
export declare const BsFillBalloonFill: IconType;
export declare const BsFillBalloonHeartFill: IconType;
export declare const BsFillBanFill: IconType;
export declare const BsFillBandaidFill: IconType;
export declare const BsFillBarChartFill: IconType;
export declare const BsFillBarChartLineFill: IconType;
export declare const BsFillBasketFill: IconType;
export declare const BsFillBasket2Fill: IconType;
export declare const BsFillBasket3Fill: IconType;
export declare const BsFillBellFill: IconType;
export declare const BsFillBellSlashFill: IconType;
export declare const BsFillBinocularsFill: IconType;
export declare const BsFillBookFill: IconType;
export declare const BsFillBookmarkCheckFill: IconType;
export declare const BsFillBookmarkDashFill: IconType;
export declare const BsFillBookmarkFill: IconType;
export declare const BsFillBookmarkHeartFill: IconType;
export declare const BsFillBookmarkPlusFill: IconType;
export declare const BsFillBookmarkStarFill: IconType;
export declare const BsFillBookmarkXFill: IconType;
export declare const BsFillBookmarksFill: IconType;
export declare const BsFillBoomboxFill: IconType;
export declare const BsFillBootstrapFill: IconType;
export declare const BsFillBoxFill: IconType;
export declare const BsFillBoxSeamFill: IconType;
export declare const BsFillBox2Fill: IconType;
export declare const BsFillBox2HeartFill: IconType;
export declare const BsFillBriefcaseFill: IconType;
export declare const BsFillBrightnessAltHighFill: IconType;
export declare const BsFillBrightnessAltLowFill: IconType;
export declare const BsFillBrightnessHighFill: IconType;
export declare const BsFillBrightnessLowFill: IconType;
export declare const BsFillBrushFill: IconType;
export declare const BsFillBucketFill: IconType;
export declare const BsFillBugFill: IconType;
export declare const BsFillBuildingFill: IconType;
export declare const BsFillBuildingsFill: IconType;
export declare const BsFillBusFrontFill: IconType;
export declare const BsFillCCircleFill: IconType;
export declare const BsFillCSquareFill: IconType;
export declare const BsFillCakeFill: IconType;
export declare const BsFillCake2Fill: IconType;
export declare const BsFillCalculatorFill: IconType;
export declare const BsFillCalendarCheckFill: IconType;
export declare const BsFillCalendarDateFill: IconType;
export declare const BsFillCalendarDayFill: IconType;
export declare const BsFillCalendarEventFill: IconType;
export declare const BsFillCalendarFill: IconType;
export declare const BsFillCalendarHeartFill: IconType;
export declare const BsFillCalendarMinusFill: IconType;
export declare const BsFillCalendarMonthFill: IconType;
export declare const BsFillCalendarPlusFill: IconType;
export declare const BsFillCalendarRangeFill: IconType;
export declare const BsFillCalendarWeekFill: IconType;
export declare const BsFillCalendarXFill: IconType;
export declare const BsFillCalendar2CheckFill: IconType;
export declare const BsFillCalendar2DateFill: IconType;
export declare const BsFillCalendar2DayFill: IconType;
export declare const BsFillCalendar2EventFill: IconType;
export declare const BsFillCalendar2Fill: IconType;
export declare const BsFillCalendar2HeartFill: IconType;
export declare const BsFillCalendar2MinusFill: IconType;
export declare const BsFillCalendar2MonthFill: IconType;
export declare const BsFillCalendar2PlusFill: IconType;
export declare const BsFillCalendar2RangeFill: IconType;
export declare const BsFillCalendar2WeekFill: IconType;
export declare const BsFillCalendar2XFill: IconType;
export declare const BsFillCalendar3EventFill: IconType;
export declare const BsFillCalendar3Fill: IconType;
export declare const BsFillCalendar3RangeFill: IconType;
export declare const BsFillCalendar3WeekFill: IconType;
export declare const BsFillCameraFill: IconType;
export declare const BsFillCameraReelsFill: IconType;
export declare const BsFillCameraVideoFill: IconType;
export declare const BsFillCameraVideoOffFill: IconType;
export declare const BsFillCapslockFill: IconType;
export declare const BsFillCarFrontFill: IconType;
export declare const BsFillCaretDownFill: IconType;
export declare const BsFillCaretDownSquareFill: IconType;
export declare const BsFillCaretLeftFill: IconType;
export declare const BsFillCaretLeftSquareFill: IconType;
export declare const BsFillCaretRightFill: IconType;
export declare const BsFillCaretRightSquareFill: IconType;
export declare const BsFillCaretUpFill: IconType;
export declare const BsFillCaretUpSquareFill: IconType;
export declare const BsFillCartCheckFill: IconType;
export declare const BsFillCartDashFill: IconType;
export declare const BsFillCartFill: IconType;
export declare const BsFillCartPlusFill: IconType;
export declare const BsFillCartXFill: IconType;
export declare const BsFillCassetteFill: IconType;
export declare const BsFillCcCircleFill: IconType;
export declare const BsFillCcSquareFill: IconType;
export declare const BsFillChatDotsFill: IconType;
export declare const BsFillChatFill: IconType;
export declare const BsFillChatHeartFill: IconType;
export declare const BsFillChatLeftDotsFill: IconType;
export declare const BsFillChatLeftFill: IconType;
export declare const BsFillChatLeftHeartFill: IconType;
export declare const BsFillChatLeftQuoteFill: IconType;
export declare const BsFillChatLeftTextFill: IconType;
export declare const BsFillChatQuoteFill: IconType;
export declare const BsFillChatRightDotsFill: IconType;
export declare const BsFillChatRightFill: IconType;
export declare const BsFillChatRightHeartFill: IconType;
export declare const BsFillChatRightQuoteFill: IconType;
export declare const BsFillChatRightTextFill: IconType;
export declare const BsFillChatSquareDotsFill: IconType;
export declare const BsFillChatSquareFill: IconType;
export declare const BsFillChatSquareHeartFill: IconType;
export declare const BsFillChatSquareQuoteFill: IconType;
export declare const BsFillChatSquareTextFill: IconType;
export declare const BsFillChatTextFill: IconType;
export declare const BsFillCheckCircleFill: IconType;
export declare const BsFillCheckSquareFill: IconType;
export declare const BsFillCircleFill: IconType;
export declare const BsFillClipboardCheckFill: IconType;
export declare const BsFillClipboardDataFill: IconType;
export declare const BsFillClipboardFill: IconType;
export declare const BsFillClipboardHeartFill: IconType;
export declare const BsFillClipboardMinusFill: IconType;
export declare const BsFillClipboardPlusFill: IconType;
export declare const BsFillClipboardXFill: IconType;
export declare const BsFillClipboard2CheckFill: IconType;
export declare const BsFillClipboard2DataFill: IconType;
export declare const BsFillClipboard2Fill: IconType;
export declare const BsFillClipboard2HeartFill: IconType;
export declare const BsFillClipboard2MinusFill: IconType;
export declare const BsFillClipboard2PlusFill: IconType;
export declare const BsFillClipboard2PulseFill: IconType;
export declare const BsFillClipboard2XFill: IconType;
export declare const BsFillClockFill: IconType;
export declare const BsFillCloudArrowDownFill: IconType;
export declare const BsFillCloudArrowUpFill: IconType;
export declare const BsFillCloudCheckFill: IconType;
export declare const BsFillCloudDownloadFill: IconType;
export declare const BsFillCloudDrizzleFill: IconType;
export declare const BsFillCloudFill: IconType;
export declare const BsFillCloudFogFill: IconType;
export declare const BsFillCloudFog2Fill: IconType;
export declare const BsFillCloudHailFill: IconType;
export declare const BsFillCloudHazeFill: IconType;
export declare const BsFillCloudHaze2Fill: IconType;
export declare const BsFillCloudLightningFill: IconType;
export declare const BsFillCloudLightningRainFill: IconType;
export declare const BsFillCloudMinusFill: IconType;
export declare const BsFillCloudMoonFill: IconType;
export declare const BsFillCloudPlusFill: IconType;
export declare const BsFillCloudRainFill: IconType;
export declare const BsFillCloudRainHeavyFill: IconType;
export declare const BsFillCloudSlashFill: IconType;
export declare const BsFillCloudSleetFill: IconType;
export declare const BsFillCloudSnowFill: IconType;
export declare const BsFillCloudSunFill: IconType;
export declare const BsFillCloudUploadFill: IconType;
export declare const BsFillCloudsFill: IconType;
export declare const BsFillCloudyFill: IconType;
export declare const BsFillCollectionFill: IconType;
export declare const BsFillCollectionPlayFill: IconType;
export declare const BsFillCompassFill: IconType;
export declare const BsFillCpuFill: IconType;
export declare const BsFillCreditCard2BackFill: IconType;
export declare const BsFillCreditCard2FrontFill: IconType;
export declare const BsFillCreditCardFill: IconType;
export declare const BsFillCupFill: IconType;
export declare const BsFillCupHotFill: IconType;
export declare const BsFillCursorFill: IconType;
export declare const BsFillDashCircleFill: IconType;
export declare const BsFillDashSquareFill: IconType;
export declare const BsFillDatabaseFill: IconType;
export declare const BsFillDeviceHddFill: IconType;
export declare const BsFillDeviceSsdFill: IconType;
export declare const BsFillDiagram2Fill: IconType;
export declare const BsFillDiagram3Fill: IconType;
export declare const BsFillDiamondFill: IconType;
export declare const BsFillDice1Fill: IconType;
export declare const BsFillDice2Fill: IconType;
export declare const BsFillDice3Fill: IconType;
export declare const BsFillDice4Fill: IconType;
export declare const BsFillDice5Fill: IconType;
export declare const BsFillDice6Fill: IconType;
export declare const BsFillDiscFill: IconType;
export declare const BsFillDisplayFill: IconType;
export declare const BsFillDisplayportFill: IconType;
export declare const BsFillDoorClosedFill: IconType;
export declare const BsFillDoorOpenFill: IconType;
export declare const BsFillDpadFill: IconType;
export declare const BsFillDropletFill: IconType;
export declare const BsFillDuffleFill: IconType;
export declare const BsFillEarFill: IconType;
export declare const BsFillEaselFill: IconType;
export declare const BsFillEasel2Fill: IconType;
export declare const BsFillEasel3Fill: IconType;
export declare const BsFillEggFill: IconType;
export declare const BsFillEjectFill: IconType;
export declare const BsFillEmojiAngryFill: IconType;
export declare const BsFillEmojiAstonishedFill: IconType;
export declare const BsFillEmojiDizzyFill: IconType;
export declare const BsFillEmojiExpressionlessFill: IconType;
export declare const BsFillEmojiFrownFill: IconType;
export declare const BsFillEmojiGrimaceFill: IconType;
export declare const BsFillEmojiGrinFill: IconType;
export declare const BsFillEmojiHeartEyesFill: IconType;
export declare const BsFillEmojiKissFill: IconType;
export declare const BsFillEmojiLaughingFill: IconType;
export declare const BsFillEmojiNeutralFill: IconType;
export declare const BsFillEmojiSmileFill: IconType;
export declare const BsFillEmojiSmileUpsideDownFill: IconType;
export declare const BsFillEmojiSunglassesFill: IconType;
export declare const BsFillEmojiSurpriseFill: IconType;
export declare const BsFillEmojiTearFill: IconType;
export declare const BsFillEmojiWinkFill: IconType;
export declare const BsFillEnvelopeArrowDownFill: IconType;
export declare const BsFillEnvelopeArrowUpFill: IconType;
export declare const BsFillEnvelopeAtFill: IconType;
export declare const BsFillEnvelopeCheckFill: IconType;
export declare const BsFillEnvelopeDashFill: IconType;
export declare const BsFillEnvelopeExclamationFill: IconType;
export declare const BsFillEnvelopeFill: IconType;
export declare const BsFillEnvelopeHeartFill: IconType;
export declare const BsFillEnvelopeOpenFill: IconType;
export declare const BsFillEnvelopeOpenHeartFill: IconType;
export declare const BsFillEnvelopePaperFill: IconType;
export declare const BsFillEnvelopePaperHeartFill: IconType;
export declare const BsFillEnvelopePlusFill: IconType;
export declare const BsFillEnvelopeSlashFill: IconType;
export declare const BsFillEnvelopeXFill: IconType;
export declare const BsFillEraserFill: IconType;
export declare const BsFillEvFrontFill: IconType;
export declare const BsFillEvStationFill: IconType;
export declare const BsFillExclamationCircleFill: IconType;
export declare const BsFillExclamationDiamondFill: IconType;
export declare const BsFillExclamationOctagonFill: IconType;
export declare const BsFillExclamationSquareFill: IconType;
export declare const BsFillExclamationTriangleFill: IconType;
export declare const BsFillExplicitFill: IconType;
export declare const BsFillEyeFill: IconType;
export declare const BsFillEyeSlashFill: IconType;
export declare const BsFillFastForwardBtnFill: IconType;
export declare const BsFillFastForwardCircleFill: IconType;
export declare const BsFillFastForwardFill: IconType;
export declare const BsFillFileArrowDownFill: IconType;
export declare const BsFillFileArrowUpFill: IconType;
export declare const BsFillFileBarGraphFill: IconType;
export declare const BsFillFileBinaryFill: IconType;
export declare const BsFillFileBreakFill: IconType;
export declare const BsFillFileCheckFill: IconType;
export declare const BsFillFileCodeFill: IconType;
export declare const BsFillFileDiffFill: IconType;
export declare const BsFillFileEarmarkArrowDownFill: IconType;
export declare const BsFillFileEarmarkArrowUpFill: IconType;
export declare const BsFillFileEarmarkBarGraphFill: IconType;
export declare const BsFillFileEarmarkBinaryFill: IconType;
export declare const BsFillFileEarmarkBreakFill: IconType;
export declare const BsFillFileEarmarkCheckFill: IconType;
export declare const BsFillFileEarmarkCodeFill: IconType;
export declare const BsFillFileEarmarkDiffFill: IconType;
export declare const BsFillFileEarmarkEaselFill: IconType;
export declare const BsFillFileEarmarkExcelFill: IconType;
export declare const BsFillFileEarmarkFill: IconType;
export declare const BsFillFileEarmarkFontFill: IconType;
export declare const BsFillFileEarmarkImageFill: IconType;
export declare const BsFillFileEarmarkLockFill: IconType;
export declare const BsFillFileEarmarkLock2Fill: IconType;
export declare const BsFillFileEarmarkMedicalFill: IconType;
export declare const BsFillFileEarmarkMinusFill: IconType;
export declare const BsFillFileEarmarkMusicFill: IconType;
export declare const BsFillFileEarmarkPdfFill: IconType;
export declare const BsFillFileEarmarkPersonFill: IconType;
export declare const BsFillFileEarmarkPlayFill: IconType;
export declare const BsFillFileEarmarkPlusFill: IconType;
export declare const BsFillFileEarmarkPostFill: IconType;
export declare const BsFillFileEarmarkPptFill: IconType;
export declare const BsFillFileEarmarkRichtextFill: IconType;
export declare const BsFillFileEarmarkRuledFill: IconType;
export declare const BsFillFileEarmarkSlidesFill: IconType;
export declare const BsFillFileEarmarkSpreadsheetFill: IconType;
export declare const BsFillFileEarmarkTextFill: IconType;
export declare const BsFillFileEarmarkWordFill: IconType;
export declare const BsFillFileEarmarkXFill: IconType;
export declare const BsFillFileEarmarkZipFill: IconType;
export declare const BsFillFileEaselFill: IconType;
export declare const BsFillFileExcelFill: IconType;
export declare const BsFillFileFill: IconType;
export declare const BsFillFileFontFill: IconType;
export declare const BsFillFileImageFill: IconType;
export declare const BsFillFileLockFill: IconType;
export declare const BsFillFileLock2Fill: IconType;
export declare const BsFillFileMedicalFill: IconType;
export declare const BsFillFileMinusFill: IconType;
export declare const BsFillFileMusicFill: IconType;
export declare const BsFillFilePdfFill: IconType;
export declare const BsFillFilePersonFill: IconType;
export declare const BsFillFilePlayFill: IconType;
export declare const BsFillFilePlusFill: IconType;
export declare const BsFillFilePostFill: IconType;
export declare const BsFillFilePptFill: IconType;
export declare const BsFillFileRichtextFill: IconType;
export declare const BsFillFileRuledFill: IconType;
export declare const BsFillFileSlidesFill: IconType;
export declare const BsFillFileSpreadsheetFill: IconType;
export declare const BsFillFileTextFill: IconType;
export declare const BsFillFileWordFill: IconType;
export declare const BsFillFileXFill: IconType;
export declare const BsFillFileZipFill: IconType;
export declare const BsFillFilterCircleFill: IconType;
export declare const BsFillFilterSquareFill: IconType;
export declare const BsFillFlagFill: IconType;
export declare const BsFillFloppyFill: IconType;
export declare const BsFillFloppy2Fill: IconType;
export declare const BsFillFolderFill: IconType;
export declare const BsFillFolderSymlinkFill: IconType;
export declare const BsFillForwardFill: IconType;
export declare const BsFillFuelPumpDieselFill: IconType;
export declare const BsFillFuelPumpFill: IconType;
export declare const BsFillFunnelFill: IconType;
export declare const BsFillGearFill: IconType;
export declare const BsFillGeoAltFill: IconType;
export declare const BsFillGeoFill: IconType;
export declare const BsFillGiftFill: IconType;
export declare const BsFillGrid1X2Fill: IconType;
export declare const BsFillGrid3X2GapFill: IconType;
export declare const BsFillGrid3X3GapFill: IconType;
export declare const BsFillGridFill: IconType;
export declare const BsFillHCircleFill: IconType;
export declare const BsFillHSquareFill: IconType;
export declare const BsFillHandIndexFill: IconType;
export declare const BsFillHandIndexThumbFill: IconType;
export declare const BsFillHandThumbsDownFill: IconType;
export declare const BsFillHandThumbsUpFill: IconType;
export declare const BsFillHandbagFill: IconType;
export declare const BsFillHddFill: IconType;
export declare const BsFillHddNetworkFill: IconType;
export declare const BsFillHddRackFill: IconType;
export declare const BsFillHddStackFill: IconType;
export declare const BsFillHdmiFill: IconType;
export declare const BsFillHeartFill: IconType;
export declare const BsFillHeartPulseFill: IconType;
export declare const BsFillHeartbreakFill: IconType;
export declare const BsFillHeptagonFill: IconType;
export declare const BsFillHexagonFill: IconType;
export declare const BsFillHospitalFill: IconType;
export declare const BsFillHouseAddFill: IconType;
export declare const BsFillHouseCheckFill: IconType;
export declare const BsFillHouseDashFill: IconType;
export declare const BsFillHouseDoorFill: IconType;
export declare const BsFillHouseDownFill: IconType;
export declare const BsFillHouseExclamationFill: IconType;
export declare const BsFillHouseFill: IconType;
export declare const BsFillHouseGearFill: IconType;
export declare const BsFillHouseHeartFill: IconType;
export declare const BsFillHouseLockFill: IconType;
export declare const BsFillHouseSlashFill: IconType;
export declare const BsFillHouseUpFill: IconType;
export declare const BsFillHouseXFill: IconType;
export declare const BsFillHousesFill: IconType;
export declare const BsFillImageFill: IconType;
export declare const BsFillInboxFill: IconType;
export declare const BsFillInboxesFill: IconType;
export declare const BsFillInfoCircleFill: IconType;
export declare const BsFillInfoSquareFill: IconType;
export declare const BsFillJournalBookmarkFill: IconType;
export declare const BsFillKanbanFill: IconType;
export declare const BsFillKeyFill: IconType;
export declare const BsFillKeyboardFill: IconType;
export declare const BsFillLampFill: IconType;
export declare const BsFillLaptopFill: IconType;
export declare const BsFillLayersFill: IconType;
export declare const BsFillLightbulbFill: IconType;
export declare const BsFillLightbulbOffFill: IconType;
export declare const BsFillLightningChargeFill: IconType;
export declare const BsFillLightningFill: IconType;
export declare const BsFillLockFill: IconType;
export declare const BsFillLuggageFill: IconType;
export declare const BsFillLungsFill: IconType;
export declare const BsFillMagnetFill: IconType;
export declare const BsFillMapFill: IconType;
export declare const BsFillMarkdownFill: IconType;
export declare const BsFillMegaphoneFill: IconType;
export declare const BsFillMenuAppFill: IconType;
export declare const BsFillMenuButtonFill: IconType;
export declare const BsFillMenuButtonWideFill: IconType;
export declare const BsFillMicFill: IconType;
export declare const BsFillMicMuteFill: IconType;
export declare const BsFillModemFill: IconType;
export declare const BsFillMoonFill: IconType;
export declare const BsFillMoonStarsFill: IconType;
export declare const BsFillMortarboardFill: IconType;
export declare const BsFillMotherboardFill: IconType;
export declare const BsFillMouseFill: IconType;
export declare const BsFillMouse2Fill: IconType;
export declare const BsFillMouse3Fill: IconType;
export declare const BsFillMusicPlayerFill: IconType;
export declare const BsFillNodeMinusFill: IconType;
export declare const BsFillNodePlusFill: IconType;
export declare const BsFillNutFill: IconType;
export declare const BsFillNvmeFill: IconType;
export declare const BsFillOctagonFill: IconType;
export declare const BsFillOpticalAudioFill: IconType;
export declare const BsFillPCircleFill: IconType;
export declare const BsFillPSquareFill: IconType;
export declare const BsFillPaletteFill: IconType;
export declare const BsFillPassFill: IconType;
export declare const BsFillPassportFill: IconType;
export declare const BsFillPatchCheckFill: IconType;
export declare const BsFillPatchExclamationFill: IconType;
export declare const BsFillPatchMinusFill: IconType;
export declare const BsFillPatchPlusFill: IconType;
export declare const BsFillPatchQuestionFill: IconType;
export declare const BsFillPauseBtnFill: IconType;
export declare const BsFillPauseCircleFill: IconType;
export declare const BsFillPauseFill: IconType;
export declare const BsFillPeaceFill: IconType;
export declare const BsFillPenFill: IconType;
export declare const BsFillPencilFill: IconType;
export declare const BsFillPentagonFill: IconType;
export declare const BsFillPeopleFill: IconType;
export declare const BsFillPersonBadgeFill: IconType;
export declare const BsFillPersonCheckFill: IconType;
export declare const BsFillPersonDashFill: IconType;
export declare const BsFillPersonFill: IconType;
export declare const BsFillPersonLinesFill: IconType;
export declare const BsFillPersonPlusFill: IconType;
export declare const BsFillPersonVcardFill: IconType;
export declare const BsFillPersonXFill: IconType;
export declare const BsFillPhoneFill: IconType;
export declare const BsFillPhoneLandscapeFill: IconType;
export declare const BsFillPhoneVibrateFill: IconType;
export declare const BsFillPieChartFill: IconType;
export declare const BsFillPiggyBankFill: IconType;
export declare const BsFillPinAngleFill: IconType;
export declare const BsFillPinFill: IconType;
export declare const BsFillPinMapFill: IconType;
export declare const BsFillPipFill: IconType;
export declare const BsFillPlayBtnFill: IconType;
export declare const BsFillPlayCircleFill: IconType;
export declare const BsFillPlayFill: IconType;
export declare const BsFillPlugFill: IconType;
export declare const BsFillPlusCircleFill: IconType;
export declare const BsFillPlusSquareFill: IconType;
export declare const BsFillPostageFill: IconType;
export declare const BsFillPostageHeartFill: IconType;
export declare const BsFillPostcardFill: IconType;
export declare const BsFillPostcardHeartFill: IconType;
export declare const BsFillPrinterFill: IconType;
export declare const BsFillProjectorFill: IconType;
export declare const BsFillPuzzleFill: IconType;
export declare const BsFillQuestionCircleFill: IconType;
export declare const BsFillQuestionDiamondFill: IconType;
export declare const BsFillQuestionOctagonFill: IconType;
export declare const BsFillQuestionSquareFill: IconType;
export declare const BsFillRCircleFill: IconType;
export declare const BsFillRSquareFill: IconType;
export declare const BsFillRecordBtnFill: IconType;
export declare const BsFillRecordCircleFill: IconType;
export declare const BsFillRecordFill: IconType;
export declare const BsFillRecord2Fill: IconType;
export declare const BsFillReplyAllFill: IconType;
export declare const BsFillReplyFill: IconType;
export declare const BsFillRewindBtnFill: IconType;
export declare const BsFillRewindCircleFill: IconType;
export declare const BsFillRewindFill: IconType;
export declare const BsFillRocketFill: IconType;
export declare const BsFillRocketTakeoffFill: IconType;
export declare const BsFillRouterFill: IconType;
export declare const BsFillRssFill: IconType;
export declare const BsFillSafeFill: IconType;
export declare const BsFillSafe2Fill: IconType;
export declare const BsFillSaveFill: IconType;
export declare const BsFillSave2Fill: IconType;
export declare const BsFillSdCardFill: IconType;
export declare const BsFillSearchHeartFill: IconType;
export declare const BsFillSendArrowDownFill: IconType;
export declare const BsFillSendArrowUpFill: IconType;
export declare const BsFillSendCheckFill: IconType;
export declare const BsFillSendDashFill: IconType;
export declare const BsFillSendExclamationFill: IconType;
export declare const BsFillSendFill: IconType;
export declare const BsFillSendPlusFill: IconType;
export declare const BsFillSendSlashFill: IconType;
export declare const BsFillSendXFill: IconType;
export declare const BsFillShareFill: IconType;
export declare const BsFillShieldFill: IconType;
export declare const BsFillShieldLockFill: IconType;
export declare const BsFillShieldSlashFill: IconType;
export declare const BsFillShiftFill: IconType;
export declare const BsFillSignDeadEndFill: IconType;
export declare const BsFillSignDoNotEnterFill: IconType;
export declare const BsFillSignIntersectionFill: IconType;
export declare const BsFillSignIntersectionSideFill: IconType;
export declare const BsFillSignIntersectionTFill: IconType;
export declare const BsFillSignIntersectionYFill: IconType;
export declare const BsFillSignMergeLeftFill: IconType;
export declare const BsFillSignMergeRightFill: IconType;
export declare const BsFillSignNoLeftTurnFill: IconType;
export declare const BsFillSignNoParkingFill: IconType;
export declare const BsFillSignNoRightTurnFill: IconType;
export declare const BsFillSignRailroadFill: IconType;
export declare const BsFillSignStopFill: IconType;
export declare const BsFillSignStopLightsFill: IconType;
export declare const BsFillSignTurnLeftFill: IconType;
export declare const BsFillSignTurnRightFill: IconType;
export declare const BsFillSignTurnSlightLeftFill: IconType;
export declare const BsFillSignTurnSlightRightFill: IconType;
export declare const BsFillSignYieldFill: IconType;
export declare const BsFillSignpost2Fill: IconType;
export declare const BsFillSignpostFill: IconType;
export declare const BsFillSignpostSplitFill: IconType;
export declare const BsFillSimFill: IconType;
export declare const BsFillSimSlashFill: IconType;
export declare const BsFillSkipBackwardBtnFill: IconType;
export declare const BsFillSkipBackwardCircleFill: IconType;
export declare const BsFillSkipBackwardFill: IconType;
export declare const BsFillSkipEndBtnFill: IconType;
export declare const BsFillSkipEndCircleFill: IconType;
export declare const BsFillSkipEndFill: IconType;
export declare const BsFillSkipForwardBtnFill: IconType;
export declare const BsFillSkipForwardCircleFill: IconType;
export declare const BsFillSkipForwardFill: IconType;
export declare const BsFillSkipStartBtnFill: IconType;
export declare const BsFillSkipStartCircleFill: IconType;
export declare const BsFillSkipStartFill: IconType;
export declare const BsFillSlashCircleFill: IconType;
export declare const BsFillSlashSquareFill: IconType;
export declare const BsFillSpeakerFill: IconType;
export declare const BsFillSquareFill: IconType;
export declare const BsFillStarFill: IconType;
export declare const BsFillStickiesFill: IconType;
export declare const BsFillStickyFill: IconType;
export declare const BsFillStopBtnFill: IconType;
export declare const BsFillStopCircleFill: IconType;
export declare const BsFillStopFill: IconType;
export declare const BsFillStoplightsFill: IconType;
export declare const BsFillStopwatchFill: IconType;
export declare const BsFillSuitClubFill: IconType;
export declare const BsFillSuitDiamondFill: IconType;
export declare const BsFillSuitHeartFill: IconType;
export declare const BsFillSuitSpadeFill: IconType;
export declare const BsFillSuitcaseFill: IconType;
export declare const BsFillSuitcaseLgFill: IconType;
export declare const BsFillSuitcase2Fill: IconType;
export declare const BsFillSunFill: IconType;
export declare const BsFillSunriseFill: IconType;
export declare const BsFillSunsetFill: IconType;
export declare const BsFillTabletFill: IconType;
export declare const BsFillTabletLandscapeFill: IconType;
export declare const BsFillTagFill: IconType;
export declare const BsFillTagsFill: IconType;
export declare const BsFillTaxiFrontFill: IconType;
export declare const BsFillTelephoneFill: IconType;
export declare const BsFillTelephoneForwardFill: IconType;
export declare const BsFillTelephoneInboundFill: IconType;
export declare const BsFillTelephoneMinusFill: IconType;
export declare const BsFillTelephoneOutboundFill: IconType;
export declare const BsFillTelephonePlusFill: IconType;
export declare const BsFillTelephoneXFill: IconType;
export declare const BsFillTerminalFill: IconType;
export declare const BsFillThreadsFill: IconType;
export declare const BsFillThunderboltFill: IconType;
export declare const BsFillTicketDetailedFill: IconType;
export declare const BsFillTicketFill: IconType;
export declare const BsFillTicketPerforatedFill: IconType;
export declare const BsFillTrainFreightFrontFill: IconType;
export declare const BsFillTrainFrontFill: IconType;
export declare const BsFillTrainLightrailFrontFill: IconType;
export declare const BsFillTrashFill: IconType;
export declare const BsFillTrash2Fill: IconType;
export declare const BsFillTrash3Fill: IconType;
export declare const BsFillTreeFill: IconType;
export declare const BsFillTriangleFill: IconType;
export declare const BsFillTrophyFill: IconType;
export declare const BsFillTruckFrontFill: IconType;
export declare const BsFillTvFill: IconType;
export declare const BsFillUmbrellaFill: IconType;
export declare const BsFillUnlockFill: IconType;
export declare const BsFillUsbCFill: IconType;
export declare const BsFillUsbDriveFill: IconType;
export declare const BsFillUsbFill: IconType;
export declare const BsFillUsbMicroFill: IconType;
export declare const BsFillUsbMiniFill: IconType;
export declare const BsFillUsbPlugFill: IconType;
export declare const BsFillVinylFill: IconType;
export declare const BsFillVolumeDownFill: IconType;
export declare const BsFillVolumeMuteFill: IconType;
export declare const BsFillVolumeOffFill: IconType;
export declare const BsFillVolumeUpFill: IconType;
export declare const BsFillWalletFill: IconType;
export declare const BsFillWebcamFill: IconType;
export declare const BsFillWrenchAdjustableCircleFill: IconType;
export declare const BsFillXCircleFill: IconType;
export declare const BsFillXDiamondFill: IconType;
export declare const BsFillXOctagonFill: IconType;
export declare const BsFillXSquareFill: IconType;
export declare const BsReverseBackspaceReverse: IconType;
export declare const BsReverseLayoutSidebarInsetReverse: IconType;
export declare const BsReverseLayoutSidebarReverse: IconType;
export declare const BsReverseLayoutTextSidebarReverse: IconType;
export declare const BsReverseLayoutTextWindowReverse: IconType;
export declare const BsReverseListColumnsReverse: IconType;
export declare const Bs0CircleFill: IconType;
export declare const Bs0Circle: IconType;
export declare const Bs0SquareFill: IconType;
export declare const Bs0Square: IconType;
export declare const Bs1CircleFill: IconType;
export declare const Bs1Circle: IconType;
export declare const Bs1SquareFill: IconType;
export declare const Bs1Square: IconType;
export declare const Bs123: IconType;
export declare const Bs2CircleFill: IconType;
export declare const Bs2Circle: IconType;
export declare const Bs2SquareFill: IconType;
export declare const Bs2Square: IconType;
export declare const Bs3CircleFill: IconType;
export declare const Bs3Circle: IconType;
export declare const Bs3SquareFill: IconType;
export declare const Bs3Square: IconType;
export declare const Bs4CircleFill: IconType;
export declare const Bs4Circle: IconType;
export declare const Bs4SquareFill: IconType;
export declare const Bs4Square: IconType;
export declare const Bs5CircleFill: IconType;
export declare const Bs5Circle: IconType;
export declare const Bs5SquareFill: IconType;
export declare const Bs5Square: IconType;
export declare const Bs6CircleFill: IconType;
export declare const Bs6Circle: IconType;
export declare const Bs6SquareFill: IconType;
export declare const Bs6Square: IconType;
export declare const Bs7CircleFill: IconType;
export declare const Bs7Circle: IconType;
export declare const Bs7SquareFill: IconType;
export declare const Bs7Square: IconType;
export declare const Bs8CircleFill: IconType;
export declare const Bs8Circle: IconType;
export declare const Bs8SquareFill: IconType;
export declare const Bs8Square: IconType;
export declare const Bs9CircleFill: IconType;
export declare const Bs9Circle: IconType;
export declare const Bs9SquareFill: IconType;
export declare const Bs9Square: IconType;
export declare const BsActivity: IconType;
export declare const BsAirplaneEnginesFill: IconType;
export declare const BsAirplaneEngines: IconType;
export declare const BsAirplaneFill: IconType;
export declare const BsAirplane: IconType;
export declare const BsAlarmFill: IconType;
export declare const BsAlarm: IconType;
export declare const BsAlexa: IconType;
export declare const BsAlignBottom: IconType;
export declare const BsAlignCenter: IconType;
export declare const BsAlignEnd: IconType;
export declare const BsAlignMiddle: IconType;
export declare const BsAlignStart: IconType;
export declare const BsAlignTop: IconType;
export declare const BsAlipay: IconType;
export declare const BsAlphabetUppercase: IconType;
export declare const BsAlphabet: IconType;
export declare const BsAlt: IconType;
export declare const BsAmazon: IconType;
export declare const BsAmd: IconType;
export declare const BsAndroid: IconType;
export declare const BsAndroid2: IconType;
export declare const BsAppIndicator: IconType;
export declare const BsApp: IconType;
export declare const BsApple: IconType;
export declare const BsArchiveFill: IconType;
export declare const BsArchive: IconType;
export declare const BsArrow90DegDown: IconType;
export declare const BsArrow90DegLeft: IconType;
export declare const BsArrow90DegRight: IconType;
export declare const BsArrow90DegUp: IconType;
export declare const BsArrowBarDown: IconType;
export declare const BsArrowBarLeft: IconType;
export declare const BsArrowBarRight: IconType;
export declare const BsArrowBarUp: IconType;
export declare const BsArrowClockwise: IconType;
export declare const BsArrowCounterclockwise: IconType;
export declare const BsArrowDownCircleFill: IconType;
export declare const BsArrowDownCircle: IconType;
export declare const BsArrowDownLeftCircleFill: IconType;
export declare const BsArrowDownLeftCircle: IconType;
export declare const BsArrowDownLeftSquareFill: IconType;
export declare const BsArrowDownLeftSquare: IconType;
export declare const BsArrowDownLeft: IconType;
export declare const BsArrowDownRightCircleFill: IconType;
export declare const BsArrowDownRightCircle: IconType;
export declare const BsArrowDownRightSquareFill: IconType;
export declare const BsArrowDownRightSquare: IconType;
export declare const BsArrowDownRight: IconType;
export declare const BsArrowDownShort: IconType;
export declare const BsArrowDownSquareFill: IconType;
export declare const BsArrowDownSquare: IconType;
export declare const BsArrowDownUp: IconType;
export declare const BsArrowDown: IconType;
export declare const BsArrowLeftCircleFill: IconType;
export declare const BsArrowLeftCircle: IconType;
export declare const BsArrowLeftRight: IconType;
export declare const BsArrowLeftShort: IconType;
export declare const BsArrowLeftSquareFill: IconType;
export declare const BsArrowLeftSquare: IconType;
export declare const BsArrowLeft: IconType;
export declare const BsArrowRepeat: IconType;
export declare const BsArrowReturnLeft: IconType;
export declare const BsArrowReturnRight: IconType;
export declare const BsArrowRightCircleFill: IconType;
export declare const BsArrowRightCircle: IconType;
export declare const BsArrowRightShort: IconType;
export declare const BsArrowRightSquareFill: IconType;
export declare const BsArrowRightSquare: IconType;
export declare const BsArrowRight: IconType;
export declare const BsArrowThroughHeartFill: IconType;
export declare const BsArrowThroughHeart: IconType;
export declare const BsArrowUpCircleFill: IconType;
export declare const BsArrowUpCircle: IconType;
export declare const BsArrowUpLeftCircleFill: IconType;
export declare const BsArrowUpLeftCircle: IconType;
export declare const BsArrowUpLeftSquareFill: IconType;
export declare const BsArrowUpLeftSquare: IconType;
export declare const BsArrowUpLeft: IconType;
export declare const BsArrowUpRightCircleFill: IconType;
export declare const BsArrowUpRightCircle: IconType;
export declare const BsArrowUpRightSquareFill: IconType;
export declare const BsArrowUpRightSquare: IconType;
export declare const BsArrowUpRight: IconType;
export declare const BsArrowUpShort: IconType;
export declare const BsArrowUpSquareFill: IconType;
export declare const BsArrowUpSquare: IconType;
export declare const BsArrowUp: IconType;
export declare const BsArrowsAngleContract: IconType;
export declare const BsArrowsAngleExpand: IconType;
export declare const BsArrowsCollapseVertical: IconType;
export declare const BsArrowsCollapse: IconType;
export declare const BsArrowsExpandVertical: IconType;
export declare const BsArrowsExpand: IconType;
export declare const BsArrowsFullscreen: IconType;
export declare const BsArrowsMove: IconType;
export declare const BsArrowsVertical: IconType;
export declare const BsArrows: IconType;
export declare const BsAspectRatioFill: IconType;
export declare const BsAspectRatio: IconType;
export declare const BsAsterisk: IconType;
export declare const BsAt: IconType;
export declare const BsAwardFill: IconType;
export declare const BsAward: IconType;
export declare const BsBack: IconType;
export declare const BsBackpackFill: IconType;
export declare const BsBackpack: IconType;
export declare const BsBackpack2Fill: IconType;
export declare const BsBackpack2: IconType;
export declare const BsBackpack3Fill: IconType;
export declare const BsBackpack3: IconType;
export declare const BsBackpack4Fill: IconType;
export declare const BsBackpack4: IconType;
export declare const BsBackspaceFill: IconType;
export declare const BsBackspaceReverseFill: IconType;
export declare const BsBackspaceReverse: IconType;
export declare const BsBackspace: IconType;
export declare const BsBadge3dFill: IconType;
export declare const BsBadge3D: IconType;
export declare const BsBadge4kFill: IconType;
export declare const BsBadge4K: IconType;
export declare const BsBadge8kFill: IconType;
export declare const BsBadge8K: IconType;
export declare const BsBadgeAdFill: IconType;
export declare const BsBadgeAd: IconType;
export declare const BsBadgeArFill: IconType;
export declare const BsBadgeAr: IconType;
export declare const BsBadgeCcFill: IconType;
export declare const BsBadgeCc: IconType;
export declare const BsBadgeHdFill: IconType;
export declare const BsBadgeHd: IconType;
export declare const BsBadgeSdFill: IconType;
export declare const BsBadgeSd: IconType;
export declare const BsBadgeTmFill: IconType;
export declare const BsBadgeTm: IconType;
export declare const BsBadgeVoFill: IconType;
export declare const BsBadgeVo: IconType;
export declare const BsBadgeVrFill: IconType;
export declare const BsBadgeVr: IconType;
export declare const BsBadgeWcFill: IconType;
export declare const BsBadgeWc: IconType;
export declare const BsBagCheckFill: IconType;
export declare const BsBagCheck: IconType;
export declare const BsBagDashFill: IconType;
export declare const BsBagDash: IconType;
export declare const BsBagFill: IconType;
export declare const BsBagHeartFill: IconType;
export declare const BsBagHeart: IconType;
export declare const BsBagPlusFill: IconType;
export declare const BsBagPlus: IconType;
export declare const BsBagXFill: IconType;
export declare const BsBagX: IconType;
export declare const BsBag: IconType;
export declare const BsBalloonFill: IconType;
export declare const BsBalloonHeartFill: IconType;
export declare const BsBalloonHeart: IconType;
export declare const BsBalloon: IconType;
export declare const BsBanFill: IconType;
export declare const BsBan: IconType;
export declare const BsBandaidFill: IconType;
export declare const BsBandaid: IconType;
export declare const BsBank: IconType;
export declare const BsBank2: IconType;
export declare const BsBarChartFill: IconType;
export declare const BsBarChartLineFill: IconType;
export declare const BsBarChartLine: IconType;
export declare const BsBarChartSteps: IconType;
export declare const BsBarChart: IconType;
export declare const BsBasketFill: IconType;
export declare const BsBasket: IconType;
export declare const BsBasket2Fill: IconType;
export declare const BsBasket2: IconType;
export declare const BsBasket3Fill: IconType;
export declare const BsBasket3: IconType;
export declare const BsBatteryCharging: IconType;
export declare const BsBatteryFull: IconType;
export declare const BsBatteryHalf: IconType;
export declare const BsBattery: IconType;
export declare const BsBehance: IconType;
export declare const BsBellFill: IconType;
export declare const BsBellSlashFill: IconType;
export declare const BsBellSlash: IconType;
export declare const BsBell: IconType;
export declare const BsBezier: IconType;
export declare const BsBezier2: IconType;
export declare const BsBicycle: IconType;
export declare const BsBing: IconType;
export declare const BsBinocularsFill: IconType;
export declare const BsBinoculars: IconType;
export declare const BsBlockquoteLeft: IconType;
export declare const BsBlockquoteRight: IconType;
export declare const BsBluetooth: IconType;
export declare const BsBodyText: IconType;
export declare const BsBookFill: IconType;
export declare const BsBookHalf: IconType;
export declare const BsBook: IconType;
export declare const BsBookmarkCheckFill: IconType;
export declare const BsBookmarkCheck: IconType;
export declare const BsBookmarkDashFill: IconType;
export declare const BsBookmarkDash: IconType;
export declare const BsBookmarkFill: IconType;
export declare const BsBookmarkHeartFill: IconType;
export declare const BsBookmarkHeart: IconType;
export declare const BsBookmarkPlusFill: IconType;
export declare const BsBookmarkPlus: IconType;
export declare const BsBookmarkStarFill: IconType;
export declare const BsBookmarkStar: IconType;
export declare const BsBookmarkXFill: IconType;
export declare const BsBookmarkX: IconType;
export declare const BsBookmark: IconType;
export declare const BsBookmarksFill: IconType;
export declare const BsBookmarks: IconType;
export declare const BsBookshelf: IconType;
export declare const BsBoomboxFill: IconType;
export declare const BsBoombox: IconType;
export declare const BsBootstrapFill: IconType;
export declare const BsBootstrapReboot: IconType;
export declare const BsBootstrap: IconType;
export declare const BsBorderAll: IconType;
export declare const BsBorderBottom: IconType;
export declare const BsBorderCenter: IconType;
export declare const BsBorderInner: IconType;
export declare const BsBorderLeft: IconType;
export declare const BsBorderMiddle: IconType;
export declare const BsBorderOuter: IconType;
export declare const BsBorderRight: IconType;
export declare const BsBorderStyle: IconType;
export declare const BsBorderTop: IconType;
export declare const BsBorderWidth: IconType;
export declare const BsBorder: IconType;
export declare const BsBoundingBoxCircles: IconType;
export declare const BsBoundingBox: IconType;
export declare const BsBoxArrowDownLeft: IconType;
export declare const BsBoxArrowDownRight: IconType;
export declare const BsBoxArrowDown: IconType;
export declare const BsBoxArrowInDownLeft: IconType;
export declare const BsBoxArrowInDownRight: IconType;
export declare const BsBoxArrowInDown: IconType;
export declare const BsBoxArrowInLeft: IconType;
export declare const BsBoxArrowInRight: IconType;
export declare const BsBoxArrowInUpLeft: IconType;
export declare const BsBoxArrowInUpRight: IconType;
export declare const BsBoxArrowInUp: IconType;
export declare const BsBoxArrowLeft: IconType;
export declare const BsBoxArrowRight: IconType;
export declare const BsBoxArrowUpLeft: IconType;
export declare const BsBoxArrowUpRight: IconType;
export declare const BsBoxArrowUp: IconType;
export declare const BsBoxFill: IconType;
export declare const BsBoxSeamFill: IconType;
export declare const BsBoxSeam: IconType;
export declare const BsBox: IconType;
export declare const BsBox2Fill: IconType;
export declare const BsBox2HeartFill: IconType;
export declare const BsBox2Heart: IconType;
export declare const BsBox2: IconType;
export declare const BsBoxes: IconType;
export declare const BsBracesAsterisk: IconType;
export declare const BsBraces: IconType;
export declare const BsBricks: IconType;
export declare const BsBriefcaseFill: IconType;
export declare const BsBriefcase: IconType;
export declare const BsBrightnessAltHighFill: IconType;
export declare const BsBrightnessAltHigh: IconType;
export declare const BsBrightnessAltLowFill: IconType;
export declare const BsBrightnessAltLow: IconType;
export declare const BsBrightnessHighFill: IconType;
export declare const BsBrightnessHigh: IconType;
export declare const BsBrightnessLowFill: IconType;
export declare const BsBrightnessLow: IconType;
export declare const BsBrilliance: IconType;
export declare const BsBroadcastPin: IconType;
export declare const BsBroadcast: IconType;
export declare const BsBrowserChrome: IconType;
export declare const BsBrowserEdge: IconType;
export declare const BsBrowserFirefox: IconType;
export declare const BsBrowserSafari: IconType;
export declare const BsBrushFill: IconType;
export declare const BsBrush: IconType;
export declare const BsBucketFill: IconType;
export declare const BsBucket: IconType;
export declare const BsBugFill: IconType;
export declare const BsBug: IconType;
export declare const BsBuildingAdd: IconType;
export declare const BsBuildingCheck: IconType;
export declare const BsBuildingDash: IconType;
export declare const BsBuildingDown: IconType;
export declare const BsBuildingExclamation: IconType;
export declare const BsBuildingFillAdd: IconType;
export declare const BsBuildingFillCheck: IconType;
export declare const BsBuildingFillDash: IconType;
export declare const BsBuildingFillDown: IconType;
export declare const BsBuildingFillExclamation: IconType;
export declare const BsBuildingFillGear: IconType;
export declare const BsBuildingFillLock: IconType;
export declare const BsBuildingFillSlash: IconType;
export declare const BsBuildingFillUp: IconType;
export declare const BsBuildingFillX: IconType;
export declare const BsBuildingFill: IconType;
export declare const BsBuildingGear: IconType;
export declare const BsBuildingLock: IconType;
export declare const BsBuildingSlash: IconType;
export declare const BsBuildingUp: IconType;
export declare const BsBuildingX: IconType;
export declare const BsBuilding: IconType;
export declare const BsBuildingsFill: IconType;
export declare const BsBuildings: IconType;
export declare const BsBullseye: IconType;
export declare const BsBusFrontFill: IconType;
export declare const BsBusFront: IconType;
export declare const BsCCircleFill: IconType;
export declare const BsCCircle: IconType;
export declare const BsCSquareFill: IconType;
export declare const BsCSquare: IconType;
export declare const BsCakeFill: IconType;
export declare const BsCake: IconType;
export declare const BsCake2Fill: IconType;
export declare const BsCake2: IconType;
export declare const BsCalculatorFill: IconType;
export declare const BsCalculator: IconType;
export declare const BsCalendarCheckFill: IconType;
export declare const BsCalendarCheck: IconType;
export declare const BsCalendarDateFill: IconType;
export declare const BsCalendarDate: IconType;
export declare const BsCalendarDayFill: IconType;
export declare const BsCalendarDay: IconType;
export declare const BsCalendarEventFill: IconType;
export declare const BsCalendarEvent: IconType;
export declare const BsCalendarFill: IconType;
export declare const BsCalendarHeartFill: IconType;
export declare const BsCalendarHeart: IconType;
export declare const BsCalendarMinusFill: IconType;
export declare const BsCalendarMinus: IconType;
export declare const BsCalendarMonthFill: IconType;
export declare const BsCalendarMonth: IconType;
export declare const BsCalendarPlusFill: IconType;
export declare const BsCalendarPlus: IconType;
export declare const BsCalendarRangeFill: IconType;
export declare const BsCalendarRange: IconType;
export declare const BsCalendarWeekFill: IconType;
export declare const BsCalendarWeek: IconType;
export declare const BsCalendarXFill: IconType;
export declare const BsCalendarX: IconType;
export declare const BsCalendar: IconType;
export declare const BsCalendar2CheckFill: IconType;
export declare const BsCalendar2Check: IconType;
export declare const BsCalendar2DateFill: IconType;
export declare const BsCalendar2Date: IconType;
export declare const BsCalendar2DayFill: IconType;
export declare const BsCalendar2Day: IconType;
export declare const BsCalendar2EventFill: IconType;
export declare const BsCalendar2Event: IconType;
export declare const BsCalendar2Fill: IconType;
export declare const BsCalendar2HeartFill: IconType;
export declare const BsCalendar2Heart: IconType;
export declare const BsCalendar2MinusFill: IconType;
export declare const BsCalendar2Minus: IconType;
export declare const BsCalendar2MonthFill: IconType;
export declare const BsCalendar2Month: IconType;
export declare const BsCalendar2PlusFill: IconType;
export declare const BsCalendar2Plus: IconType;
export declare const BsCalendar2RangeFill: IconType;
export declare const BsCalendar2Range: IconType;
export declare const BsCalendar2WeekFill: IconType;
export declare const BsCalendar2Week: IconType;
export declare const BsCalendar2XFill: IconType;
export declare const BsCalendar2X: IconType;
export declare const BsCalendar2: IconType;
export declare const BsCalendar3EventFill: IconType;
export declare const BsCalendar3Event: IconType;
export declare const BsCalendar3Fill: IconType;
export declare const BsCalendar3RangeFill: IconType;
export declare const BsCalendar3Range: IconType;
export declare const BsCalendar3WeekFill: IconType;
export declare const BsCalendar3Week: IconType;
export declare const BsCalendar3: IconType;
export declare const BsCalendar4Event: IconType;
export declare const BsCalendar4Range: IconType;
export declare const BsCalendar4Week: IconType;
export declare const BsCalendar4: IconType;
export declare const BsCameraFill: IconType;
export declare const BsCameraReelsFill: IconType;
export declare const BsCameraReels: IconType;
export declare const BsCameraVideoFill: IconType;
export declare const BsCameraVideoOffFill: IconType;
export declare const BsCameraVideoOff: IconType;
export declare const BsCameraVideo: IconType;
export declare const BsCamera: IconType;
export declare const BsCamera2: IconType;
export declare const BsCapslockFill: IconType;
export declare const BsCapslock: IconType;
export declare const BsCapsulePill: IconType;
export declare const BsCapsule: IconType;
export declare const BsCarFrontFill: IconType;
export declare const BsCarFront: IconType;
export declare const BsCardChecklist: IconType;
export declare const BsCardHeading: IconType;
export declare const BsCardImage: IconType;
export declare const BsCardList: IconType;
export declare const BsCardText: IconType;
export declare const BsCaretDownFill: IconType;
export declare const BsCaretDownSquareFill: IconType;
export declare const BsCaretDownSquare: IconType;
export declare const BsCaretDown: IconType;
export declare const BsCaretLeftFill: IconType;
export declare const BsCaretLeftSquareFill: IconType;
export declare const BsCaretLeftSquare: IconType;
export declare const BsCaretLeft: IconType;
export declare const BsCaretRightFill: IconType;
export declare const BsCaretRightSquareFill: IconType;
export declare const BsCaretRightSquare: IconType;
export declare const BsCaretRight: IconType;
export declare const BsCaretUpFill: IconType;
export declare const BsCaretUpSquareFill: IconType;
export declare const BsCaretUpSquare: IconType;
export declare const BsCaretUp: IconType;
export declare const BsCartCheckFill: IconType;
export declare const BsCartCheck: IconType;
export declare const BsCartDashFill: IconType;
export declare const BsCartDash: IconType;
export declare const BsCartFill: IconType;
export declare const BsCartPlusFill: IconType;
export declare const BsCartPlus: IconType;
export declare const BsCartXFill: IconType;
export declare const BsCartX: IconType;
export declare const BsCart: IconType;
export declare const BsCart2: IconType;
export declare const BsCart3: IconType;
export declare const BsCart4: IconType;
export declare const BsCashCoin: IconType;
export declare const BsCashStack: IconType;
export declare const BsCash: IconType;
export declare const BsCassetteFill: IconType;
export declare const BsCassette: IconType;
export declare const BsCast: IconType;
export declare const BsCcCircleFill: IconType;
export declare const BsCcCircle: IconType;
export declare const BsCcSquareFill: IconType;
export declare const BsCcSquare: IconType;
export declare const BsChatDotsFill: IconType;
export declare const BsChatDots: IconType;
export declare const BsChatFill: IconType;
export declare const BsChatHeartFill: IconType;
export declare const BsChatHeart: IconType;
export declare const BsChatLeftDotsFill: IconType;
export declare const BsChatLeftDots: IconType;
export declare const BsChatLeftFill: IconType;
export declare const BsChatLeftHeartFill: IconType;
export declare const BsChatLeftHeart: IconType;
export declare const BsChatLeftQuoteFill: IconType;
export declare const BsChatLeftQuote: IconType;
export declare const BsChatLeftTextFill: IconType;
export declare const BsChatLeftText: IconType;
export declare const BsChatLeft: IconType;
export declare const BsChatQuoteFill: IconType;
export declare const BsChatQuote: IconType;
export declare const BsChatRightDotsFill: IconType;
export declare const BsChatRightDots: IconType;
export declare const BsChatRightFill: IconType;
export declare const BsChatRightHeartFill: IconType;
export declare const BsChatRightHeart: IconType;
export declare const BsChatRightQuoteFill: IconType;
export declare const BsChatRightQuote: IconType;
export declare const BsChatRightTextFill: IconType;
export declare const BsChatRightText: IconType;
export declare const BsChatRight: IconType;
export declare const BsChatSquareDotsFill: IconType;
export declare const BsChatSquareDots: IconType;
export declare const BsChatSquareFill: IconType;
export declare const BsChatSquareHeartFill: IconType;
export declare const BsChatSquareHeart: IconType;
export declare const BsChatSquareQuoteFill: IconType;
export declare const BsChatSquareQuote: IconType;
export declare const BsChatSquareTextFill: IconType;
export declare const BsChatSquareText: IconType;
export declare const BsChatSquare: IconType;
export declare const BsChatTextFill: IconType;
export declare const BsChatText: IconType;
export declare const BsChat: IconType;
export declare const BsCheckAll: IconType;
export declare const BsCheckCircleFill: IconType;
export declare const BsCheckCircle: IconType;
export declare const BsCheckLg: IconType;
export declare const BsCheckSquareFill: IconType;
export declare const BsCheckSquare: IconType;
export declare const BsCheck: IconType;
export declare const BsCheck2All: IconType;
export declare const BsCheck2Circle: IconType;
export declare const BsCheck2Square: IconType;
export declare const BsCheck2: IconType;
export declare const BsChevronBarContract: IconType;
export declare const BsChevronBarDown: IconType;
export declare const BsChevronBarExpand: IconType;
export declare const BsChevronBarLeft: IconType;
export declare const BsChevronBarRight: IconType;
export declare const BsChevronBarUp: IconType;
export declare const BsChevronCompactDown: IconType;
export declare const BsChevronCompactLeft: IconType;
export declare const BsChevronCompactRight: IconType;
export declare const BsChevronCompactUp: IconType;
export declare const BsChevronContract: IconType;
export declare const BsChevronDoubleDown: IconType;
export declare const BsChevronDoubleLeft: IconType;
export declare const BsChevronDoubleRight: IconType;
export declare const BsChevronDoubleUp: IconType;
export declare const BsChevronDown: IconType;
export declare const BsChevronExpand: IconType;
export declare const BsChevronLeft: IconType;
export declare const BsChevronRight: IconType;
export declare const BsChevronUp: IconType;
export declare const BsCircleFill: IconType;
export declare const BsCircleHalf: IconType;
export declare const BsCircleSquare: IconType;
export declare const BsCircle: IconType;
export declare const BsClipboardCheckFill: IconType;
export declare const BsClipboardCheck: IconType;
export declare const BsClipboardDataFill: IconType;
export declare const BsClipboardData: IconType;
export declare const BsClipboardFill: IconType;
export declare const BsClipboardHeartFill: IconType;
export declare const BsClipboardHeart: IconType;
export declare const BsClipboardMinusFill: IconType;
export declare const BsClipboardMinus: IconType;
export declare const BsClipboardPlusFill: IconType;
export declare const BsClipboardPlus: IconType;
export declare const BsClipboardPulse: IconType;
export declare const BsClipboardXFill: IconType;
export declare const BsClipboardX: IconType;
export declare const BsClipboard: IconType;
export declare const BsClipboard2CheckFill: IconType;
export declare const BsClipboard2Check: IconType;
export declare const BsClipboard2DataFill: IconType;
export declare const BsClipboard2Data: IconType;
export declare const BsClipboard2Fill: IconType;
export declare const BsClipboard2HeartFill: IconType;
export declare const BsClipboard2Heart: IconType;
export declare const BsClipboard2MinusFill: IconType;
export declare const BsClipboard2Minus: IconType;
export declare const BsClipboard2PlusFill: IconType;
export declare const BsClipboard2Plus: IconType;
export declare const BsClipboard2PulseFill: IconType;
export declare const BsClipboard2Pulse: IconType;
export declare const BsClipboard2XFill: IconType;
export declare const BsClipboard2X: IconType;
export declare const BsClipboard2: IconType;
export declare const BsClockFill: IconType;
export declare const BsClockHistory: IconType;
export declare const BsClock: IconType;
export declare const BsCloudArrowDownFill: IconType;
export declare const BsCloudArrowDown: IconType;
export declare const BsCloudArrowUpFill: IconType;
export declare const BsCloudArrowUp: IconType;
export declare const BsCloudCheckFill: IconType;
export declare const BsCloudCheck: IconType;
export declare const BsCloudDownloadFill: IconType;
export declare const BsCloudDownload: IconType;
export declare const BsCloudDrizzleFill: IconType;
export declare const BsCloudDrizzle: IconType;
export declare const BsCloudFill: IconType;
export declare const BsCloudFogFill: IconType;
export declare const BsCloudFog: IconType;
export declare const BsCloudFog2Fill: IconType;
export declare const BsCloudFog2: IconType;
export declare const BsCloudHailFill: IconType;
export declare const BsCloudHail: IconType;
export declare const BsCloudHazeFill: IconType;
export declare const BsCloudHaze: IconType;
export declare const BsCloudHaze2Fill: IconType;
export declare const BsCloudHaze2: IconType;
export declare const BsCloudLightningFill: IconType;
export declare const BsCloudLightningRainFill: IconType;
export declare const BsCloudLightningRain: IconType;
export declare const BsCloudLightning: IconType;
export declare const BsCloudMinusFill: IconType;
export declare const BsCloudMinus: IconType;
export declare const BsCloudMoonFill: IconType;
export declare const BsCloudMoon: IconType;
export declare const BsCloudPlusFill: IconType;
export declare const BsCloudPlus: IconType;
export declare const BsCloudRainFill: IconType;
export declare const BsCloudRainHeavyFill: IconType;
export declare const BsCloudRainHeavy: IconType;
export declare const BsCloudRain: IconType;
export declare const BsCloudSlashFill: IconType;
export declare const BsCloudSlash: IconType;
export declare const BsCloudSleetFill: IconType;
export declare const BsCloudSleet: IconType;
export declare const BsCloudSnowFill: IconType;
export declare const BsCloudSnow: IconType;
export declare const BsCloudSunFill: IconType;
export declare const BsCloudSun: IconType;
export declare const BsCloudUploadFill: IconType;
export declare const BsCloudUpload: IconType;
export declare const BsCloud: IconType;
export declare const BsCloudsFill: IconType;
export declare const BsClouds: IconType;
export declare const BsCloudyFill: IconType;
export declare const BsCloudy: IconType;
export declare const BsCodeSlash: IconType;
export declare const BsCodeSquare: IconType;
export declare const BsCode: IconType;
export declare const BsCoin: IconType;
export declare const BsCollectionFill: IconType;
export declare const BsCollectionPlayFill: IconType;
export declare const BsCollectionPlay: IconType;
export declare const BsCollection: IconType;
export declare const BsColumnsGap: IconType;
export declare const BsColumns: IconType;
export declare const BsCommand: IconType;
export declare const BsCompassFill: IconType;
export declare const BsCompass: IconType;
export declare const BsConeStriped: IconType;
export declare const BsCone: IconType;
export declare const BsController: IconType;
export declare const BsCookie: IconType;
export declare const BsCopy: IconType;
export declare const BsCpuFill: IconType;
export declare const BsCpu: IconType;
export declare const BsCreditCard2BackFill: IconType;
export declare const BsCreditCard2Back: IconType;
export declare const BsCreditCard2FrontFill: IconType;
export declare const BsCreditCard2Front: IconType;
export declare const BsCreditCardFill: IconType;
export declare const BsCreditCard: IconType;
export declare const BsCrop: IconType;
export declare const BsCrosshair: IconType;
export declare const BsCrosshair2: IconType;
export declare const BsCupFill: IconType;
export declare const BsCupHotFill: IconType;
export declare const BsCupHot: IconType;
export declare const BsCupStraw: IconType;
export declare const BsCup: IconType;
export declare const BsCurrencyBitcoin: IconType;
export declare const BsCurrencyDollar: IconType;
export declare const BsCurrencyEuro: IconType;
export declare const BsCurrencyExchange: IconType;
export declare const BsCurrencyPound: IconType;
export declare const BsCurrencyRupee: IconType;
export declare const BsCurrencyYen: IconType;
export declare const BsCursorFill: IconType;
export declare const BsCursorText: IconType;
export declare const BsCursor: IconType;
export declare const BsDashCircleDotted: IconType;
export declare const BsDashCircleFill: IconType;
export declare const BsDashCircle: IconType;
export declare const BsDashLg: IconType;
export declare const BsDashSquareDotted: IconType;
export declare const BsDashSquareFill: IconType;
export declare const BsDashSquare: IconType;
export declare const BsDash: IconType;
export declare const BsDatabaseAdd: IconType;
export declare const BsDatabaseCheck: IconType;
export declare const BsDatabaseDash: IconType;
export declare const BsDatabaseDown: IconType;
export declare const BsDatabaseExclamation: IconType;
export declare const BsDatabaseFillAdd: IconType;
export declare const BsDatabaseFillCheck: IconType;
export declare const BsDatabaseFillDash: IconType;
export declare const BsDatabaseFillDown: IconType;
export declare const BsDatabaseFillExclamation: IconType;
export declare const BsDatabaseFillGear: IconType;
export declare const BsDatabaseFillLock: IconType;
export declare const BsDatabaseFillSlash: IconType;
export declare const BsDatabaseFillUp: IconType;
export declare const BsDatabaseFillX: IconType;
export declare const BsDatabaseFill: IconType;
export declare const BsDatabaseGear: IconType;
export declare const BsDatabaseLock: IconType;
export declare const BsDatabaseSlash: IconType;
export declare const BsDatabaseUp: IconType;
export declare const BsDatabaseX: IconType;
export declare const BsDatabase: IconType;
export declare const BsDeviceHddFill: IconType;
export declare const BsDeviceHdd: IconType;
export declare const BsDeviceSsdFill: IconType;
export declare const BsDeviceSsd: IconType;
export declare const BsDiagram2Fill: IconType;
export declare const BsDiagram2: IconType;
export declare const BsDiagram3Fill: IconType;
export declare const BsDiagram3: IconType;
export declare const BsDiamondFill: IconType;
export declare const BsDiamondHalf: IconType;
export declare const BsDiamond: IconType;
export declare const BsDice1Fill: IconType;
export declare const BsDice1: IconType;
export declare const BsDice2Fill: IconType;
export declare const BsDice2: IconType;
export declare const BsDice3Fill: IconType;
export declare const BsDice3: IconType;
export declare const BsDice4Fill: IconType;
export declare const BsDice4: IconType;
export declare const BsDice5Fill: IconType;
export declare const BsDice5: IconType;
export declare const BsDice6Fill: IconType;
export declare const BsDice6: IconType;
export declare const BsDiscFill: IconType;
export declare const BsDisc: IconType;
export declare const BsDiscord: IconType;
export declare const BsDisplayFill: IconType;
export declare const BsDisplay: IconType;
export declare const BsDisplayportFill: IconType;
export declare const BsDisplayport: IconType;
export declare const BsDistributeHorizontal: IconType;
export declare const BsDistributeVertical: IconType;
export declare const BsDoorClosedFill: IconType;
export declare const BsDoorClosed: IconType;
export declare const BsDoorOpenFill: IconType;
export declare const BsDoorOpen: IconType;
export declare const BsDot: IconType;
export declare const BsDownload: IconType;
export declare const BsDpadFill: IconType;
export declare const BsDpad: IconType;
export declare const BsDribbble: IconType;
export declare const BsDropbox: IconType;
export declare const BsDropletFill: IconType;
export declare const BsDropletHalf: IconType;
export declare const BsDroplet: IconType;
export declare const BsDuffleFill: IconType;
export declare const BsDuffle: IconType;
export declare const BsEarFill: IconType;
export declare const BsEar: IconType;
export declare const BsEarbuds: IconType;
export declare const BsEaselFill: IconType;
export declare const BsEasel: IconType;
export declare const BsEasel2Fill: IconType;
export declare const BsEasel2: IconType;
export declare const BsEasel3Fill: IconType;
export declare const BsEasel3: IconType;
export declare const BsEggFill: IconType;
export declare const BsEggFried: IconType;
export declare const BsEgg: IconType;
export declare const BsEjectFill: IconType;
export declare const BsEject: IconType;
export declare const BsEmojiAngryFill: IconType;
export declare const BsEmojiAngry: IconType;
export declare const BsEmojiAstonishedFill: IconType;
export declare const BsEmojiAstonished: IconType;
export declare const BsEmojiDizzyFill: IconType;
export declare const BsEmojiDizzy: IconType;
export declare const BsEmojiExpressionlessFill: IconType;
export declare const BsEmojiExpressionless: IconType;
export declare const BsEmojiFrownFill: IconType;
export declare const BsEmojiFrown: IconType;
export declare const BsEmojiGrimaceFill: IconType;
export declare const BsEmojiGrimace: IconType;
export declare const BsEmojiGrinFill: IconType;
export declare const BsEmojiGrin: IconType;
export declare const BsEmojiHeartEyesFill: IconType;
export declare const BsEmojiHeartEyes: IconType;
export declare const BsEmojiKissFill: IconType;
export declare const BsEmojiKiss: IconType;
export declare const BsEmojiLaughingFill: IconType;
export declare const BsEmojiLaughing: IconType;
export declare const BsEmojiNeutralFill: IconType;
export declare const BsEmojiNeutral: IconType;
export declare const BsEmojiSmileFill: IconType;
export declare const BsEmojiSmileUpsideDownFill: IconType;
export declare const BsEmojiSmileUpsideDown: IconType;
export declare const BsEmojiSmile: IconType;
export declare const BsEmojiSunglassesFill: IconType;
export declare const BsEmojiSunglasses: IconType;
export declare const BsEmojiSurpriseFill: IconType;
export declare const BsEmojiSurprise: IconType;
export declare const BsEmojiTearFill: IconType;
export declare const BsEmojiTear: IconType;
export declare const BsEmojiWinkFill: IconType;
export declare const BsEmojiWink: IconType;
export declare const BsEnvelopeArrowDownFill: IconType;
export declare const BsEnvelopeArrowDown: IconType;
export declare const BsEnvelopeArrowUpFill: IconType;
export declare const BsEnvelopeArrowUp: IconType;
export declare const BsEnvelopeAtFill: IconType;
export declare const BsEnvelopeAt: IconType;
export declare const BsEnvelopeCheckFill: IconType;
export declare const BsEnvelopeCheck: IconType;
export declare const BsEnvelopeDashFill: IconType;
export declare const BsEnvelopeDash: IconType;
export declare const BsEnvelopeExclamationFill: IconType;
export declare const BsEnvelopeExclamation: IconType;
export declare const BsEnvelopeFill: IconType;
export declare const BsEnvelopeHeartFill: IconType;
export declare const BsEnvelopeHeart: IconType;
export declare const BsEnvelopeOpenFill: IconType;
export declare const BsEnvelopeOpenHeartFill: IconType;
export declare const BsEnvelopeOpenHeart: IconType;
export declare const BsEnvelopeOpen: IconType;
export declare const BsEnvelopePaperFill: IconType;
export declare const BsEnvelopePaperHeartFill: IconType;
export declare const BsEnvelopePaperHeart: IconType;
export declare const BsEnvelopePaper: IconType;
export declare const BsEnvelopePlusFill: IconType;
export declare const BsEnvelopePlus: IconType;
export declare const BsEnvelopeSlashFill: IconType;
export declare const BsEnvelopeSlash: IconType;
export declare const BsEnvelopeXFill: IconType;
export declare const BsEnvelopeX: IconType;
export declare const BsEnvelope: IconType;
export declare const BsEraserFill: IconType;
export declare const BsEraser: IconType;
export declare const BsEscape: IconType;
export declare const BsEthernet: IconType;
export declare const BsEvFrontFill: IconType;
export declare const BsEvFront: IconType;
export declare const BsEvStationFill: IconType;
export declare const BsEvStation: IconType;
export declare const BsExclamationCircleFill: IconType;
export declare const BsExclamationCircle: IconType;
export declare const BsExclamationDiamondFill: IconType;
export declare const BsExclamationDiamond: IconType;
export declare const BsExclamationLg: IconType;
export declare const BsExclamationOctagonFill: IconType;
export declare const BsExclamationOctagon: IconType;
export declare const BsExclamationSquareFill: IconType;
export declare const BsExclamationSquare: IconType;
export declare const BsExclamationTriangleFill: IconType;
export declare const BsExclamationTriangle: IconType;
export declare const BsExclamation: IconType;
export declare const BsExclude: IconType;
export declare const BsExplicitFill: IconType;
export declare const BsExplicit: IconType;
export declare const BsExposure: IconType;
export declare const BsEyeFill: IconType;
export declare const BsEyeSlashFill: IconType;
export declare const BsEyeSlash: IconType;
export declare const BsEye: IconType;
export declare const BsEyedropper: IconType;
export declare const BsEyeglasses: IconType;
export declare const BsFacebook: IconType;
export declare const BsFan: IconType;
export declare const BsFastForwardBtnFill: IconType;
export declare const BsFastForwardBtn: IconType;
export declare const BsFastForwardCircleFill: IconType;
export declare const BsFastForwardCircle: IconType;
export declare const BsFastForwardFill: IconType;
export declare const BsFastForward: IconType;
export declare const BsFeather: IconType;
export declare const BsFeather2: IconType;
export declare const BsFileArrowDownFill: IconType;
export declare const BsFileArrowDown: IconType;
export declare const BsFileArrowUpFill: IconType;
export declare const BsFileArrowUp: IconType;
export declare const BsFileBarGraphFill: IconType;
export declare const BsFileBarGraph: IconType;
export declare const BsFileBinaryFill: IconType;
export declare const BsFileBinary: IconType;
export declare const BsFileBreakFill: IconType;
export declare const BsFileBreak: IconType;
export declare const BsFileCheckFill: IconType;
export declare const BsFileCheck: IconType;
export declare const BsFileCodeFill: IconType;
export declare const BsFileCode: IconType;
export declare const BsFileDiffFill: IconType;
export declare const BsFileDiff: IconType;
export declare const BsFileEarmarkArrowDownFill: IconType;
export declare const BsFileEarmarkArrowDown: IconType;
export declare const BsFileEarmarkArrowUpFill: IconType;
export declare const BsFileEarmarkArrowUp: IconType;
export declare const BsFileEarmarkBarGraphFill: IconType;
export declare const BsFileEarmarkBarGraph: IconType;
export declare const BsFileEarmarkBinaryFill: IconType;
export declare const BsFileEarmarkBinary: IconType;
export declare const BsFileEarmarkBreakFill: IconType;
export declare const BsFileEarmarkBreak: IconType;
export declare const BsFileEarmarkCheckFill: IconType;
export declare const BsFileEarmarkCheck: IconType;
export declare const BsFileEarmarkCodeFill: IconType;
export declare const BsFileEarmarkCode: IconType;
export declare const BsFileEarmarkDiffFill: IconType;
export declare const BsFileEarmarkDiff: IconType;
export declare const BsFileEarmarkEaselFill: IconType;
export declare const BsFileEarmarkEasel: IconType;
export declare const BsFileEarmarkExcelFill: IconType;
export declare const BsFileEarmarkExcel: IconType;
export declare const BsFileEarmarkFill: IconType;
export declare const BsFileEarmarkFontFill: IconType;
export declare const BsFileEarmarkFont: IconType;
export declare const BsFileEarmarkImageFill: IconType;
export declare const BsFileEarmarkImage: IconType;
export declare const BsFileEarmarkLockFill: IconType;
export declare const BsFileEarmarkLock: IconType;
export declare const BsFileEarmarkLock2Fill: IconType;
export declare const BsFileEarmarkLock2: IconType;
export declare const BsFileEarmarkMedicalFill: IconType;
export declare const BsFileEarmarkMedical: IconType;
export declare const BsFileEarmarkMinusFill: IconType;
export declare const BsFileEarmarkMinus: IconType;
export declare const BsFileEarmarkMusicFill: IconType;
export declare const BsFileEarmarkMusic: IconType;
export declare const BsFileEarmarkPdfFill: IconType;
export declare const BsFileEarmarkPdf: IconType;
export declare const BsFileEarmarkPersonFill: IconType;
export declare const BsFileEarmarkPerson: IconType;
export declare const BsFileEarmarkPlayFill: IconType;
export declare const BsFileEarmarkPlay: IconType;
export declare const BsFileEarmarkPlusFill: IconType;
export declare const BsFileEarmarkPlus: IconType;
export declare const BsFileEarmarkPostFill: IconType;
export declare const BsFileEarmarkPost: IconType;
export declare const BsFileEarmarkPptFill: IconType;
export declare const BsFileEarmarkPpt: IconType;
export declare const BsFileEarmarkRichtextFill: IconType;
export declare const BsFileEarmarkRichtext: IconType;
export declare const BsFileEarmarkRuledFill: IconType;
export declare const BsFileEarmarkRuled: IconType;
export declare const BsFileEarmarkSlidesFill: IconType;
export declare const BsFileEarmarkSlides: IconType;
export declare const BsFileEarmarkSpreadsheetFill: IconType;
export declare const BsFileEarmarkSpreadsheet: IconType;
export declare const BsFileEarmarkTextFill: IconType;
export declare const BsFileEarmarkText: IconType;
export declare const BsFileEarmarkWordFill: IconType;
export declare const BsFileEarmarkWord: IconType;
export declare const BsFileEarmarkXFill: IconType;
export declare const BsFileEarmarkX: IconType;
export declare const BsFileEarmarkZipFill: IconType;
export declare const BsFileEarmarkZip: IconType;
export declare const BsFileEarmark: IconType;
export declare const BsFileEaselFill: IconType;
export declare const BsFileEasel: IconType;
export declare const BsFileExcelFill: IconType;
export declare const BsFileExcel: IconType;
export declare const BsFileFill: IconType;
export declare const BsFileFontFill: IconType;
export declare const BsFileFont: IconType;
export declare const BsFileImageFill: IconType;
export declare const BsFileImage: IconType;
export declare const BsFileLockFill: IconType;
export declare const BsFileLock: IconType;
export declare const BsFileLock2Fill: IconType;
export declare const BsFileLock2: IconType;
export declare const BsFileMedicalFill: IconType;
export declare const BsFileMedical: IconType;
export declare const BsFileMinusFill: IconType;
export declare const BsFileMinus: IconType;
export declare const BsFileMusicFill: IconType;
export declare const BsFileMusic: IconType;
export declare const BsFilePdfFill: IconType;
export declare const BsFilePdf: IconType;
export declare const BsFilePersonFill: IconType;
export declare const BsFilePerson: IconType;
export declare const BsFilePlayFill: IconType;
export declare const BsFilePlay: IconType;
export declare const BsFilePlusFill: IconType;
export declare const BsFilePlus: IconType;
export declare const BsFilePostFill: IconType;
export declare const BsFilePost: IconType;
export declare const BsFilePptFill: IconType;
export declare const BsFilePpt: IconType;
export declare const BsFileRichtextFill: IconType;
export declare const BsFileRichtext: IconType;
export declare const BsFileRuledFill: IconType;
export declare const BsFileRuled: IconType;
export declare const BsFileSlidesFill: IconType;
export declare const BsFileSlides: IconType;
export declare const BsFileSpreadsheetFill: IconType;
export declare const BsFileSpreadsheet: IconType;
export declare const BsFileTextFill: IconType;
export declare const BsFileText: IconType;
export declare const BsFileWordFill: IconType;
export declare const BsFileWord: IconType;
export declare const BsFileXFill: IconType;
export declare const BsFileX: IconType;
export declare const BsFileZipFill: IconType;
export declare const BsFileZip: IconType;
export declare const BsFile: IconType;
export declare const BsFilesAlt: IconType;
export declare const BsFiles: IconType;
export declare const BsFiletypeAac: IconType;
export declare const BsFiletypeAi: IconType;
export declare const BsFiletypeBmp: IconType;
export declare const BsFiletypeCs: IconType;
export declare const BsFiletypeCss: IconType;
export declare const BsFiletypeCsv: IconType;
export declare const BsFiletypeDoc: IconType;
export declare const BsFiletypeDocx: IconType;
export declare const BsFiletypeExe: IconType;
export declare const BsFiletypeGif: IconType;
export declare const BsFiletypeHeic: IconType;
export declare const BsFiletypeHtml: IconType;
export declare const BsFiletypeJava: IconType;
export declare const BsFiletypeJpg: IconType;
export declare const BsFiletypeJs: IconType;
export declare const BsFiletypeJson: IconType;
export declare const BsFiletypeJsx: IconType;
export declare const BsFiletypeKey: IconType;
export declare const BsFiletypeM4P: IconType;
export declare const BsFiletypeMd: IconType;
export declare const BsFiletypeMdx: IconType;
export declare const BsFiletypeMov: IconType;
export declare const BsFiletypeMp3: IconType;
export declare const BsFiletypeMp4: IconType;
export declare const BsFiletypeOtf: IconType;
export declare const BsFiletypePdf: IconType;
export declare const BsFiletypePhp: IconType;
export declare const BsFiletypePng: IconType;
export declare const BsFiletypePpt: IconType;
export declare const BsFiletypePptx: IconType;
export declare const BsFiletypePsd: IconType;
export declare const BsFiletypePy: IconType;
export declare const BsFiletypeRaw: IconType;
export declare const BsFiletypeRb: IconType;
export declare const BsFiletypeSass: IconType;
export declare const BsFiletypeScss: IconType;
export declare const BsFiletypeSh: IconType;
export declare const BsFiletypeSql: IconType;
export declare const BsFiletypeSvg: IconType;
export declare const BsFiletypeTiff: IconType;
export declare const BsFiletypeTsx: IconType;
export declare const BsFiletypeTtf: IconType;
export declare const BsFiletypeTxt: IconType;
export declare const BsFiletypeWav: IconType;
export declare const BsFiletypeWoff: IconType;
export declare const BsFiletypeXls: IconType;
export declare const BsFiletypeXlsx: IconType;
export declare const BsFiletypeXml: IconType;
export declare const BsFiletypeYml: IconType;
export declare const BsFilm: IconType;
export declare const BsFilterCircleFill: IconType;
export declare const BsFilterCircle: IconType;
export declare const BsFilterLeft: IconType;
export declare const BsFilterRight: IconType;
export declare const BsFilterSquareFill: IconType;
export declare const BsFilterSquare: IconType;
export declare const BsFilter: IconType;
export declare const BsFingerprint: IconType;
export declare const BsFire: IconType;
export declare const BsFlagFill: IconType;
export declare const BsFlag: IconType;
export declare const BsFloppyFill: IconType;
export declare const BsFloppy: IconType;
export declare const BsFloppy2Fill: IconType;
export declare const BsFloppy2: IconType;
export declare const BsFlower1: IconType;
export declare const BsFlower2: IconType;
export declare const BsFlower3: IconType;
export declare const BsFolderCheck: IconType;
export declare const BsFolderFill: IconType;
export declare const BsFolderMinus: IconType;
export declare const BsFolderPlus: IconType;
export declare const BsFolderSymlinkFill: IconType;
export declare const BsFolderSymlink: IconType;
export declare const BsFolderX: IconType;
export declare const BsFolder: IconType;
export declare const BsFolder2Open: IconType;
export declare const BsFolder2: IconType;
export declare const BsFonts: IconType;
export declare const BsForwardFill: IconType;
export declare const BsForward: IconType;
export declare const BsFront: IconType;
export declare const BsFuelPumpDieselFill: IconType;
export declare const BsFuelPumpDiesel: IconType;
export declare const BsFuelPumpFill: IconType;
export declare const BsFuelPump: IconType;
export declare const BsFullscreenExit: IconType;
export declare const BsFullscreen: IconType;
export declare const BsFunnelFill: IconType;
export declare const BsFunnel: IconType;
export declare const BsGearFill: IconType;
export declare const BsGearWideConnected: IconType;
export declare const BsGearWide: IconType;
export declare const BsGear: IconType;
export declare const BsGem: IconType;
export declare const BsGenderAmbiguous: IconType;
export declare const BsGenderFemale: IconType;
export declare const BsGenderMale: IconType;
export declare const BsGenderNeuter: IconType;
export declare const BsGenderTrans: IconType;
export declare const BsGeoAltFill: IconType;
export declare const BsGeoAlt: IconType;
export declare const BsGeoFill: IconType;
export declare const BsGeo: IconType;
export declare const BsGiftFill: IconType;
export declare const BsGift: IconType;
export declare const BsGit: IconType;
export declare const BsGithub: IconType;
export declare const BsGitlab: IconType;
export declare const BsGlobeAmericas: IconType;
export declare const BsGlobeAsiaAustralia: IconType;
export declare const BsGlobeCentralSouthAsia: IconType;
export declare const BsGlobeEuropeAfrica: IconType;
export declare const BsGlobe: IconType;
export declare const BsGlobe2: IconType;
export declare const BsGooglePlay: IconType;
export declare const BsGoogle: IconType;
export declare const BsGpuCard: IconType;
export declare const BsGraphDownArrow: IconType;
export declare const BsGraphDown: IconType;
export declare const BsGraphUpArrow: IconType;
export declare const BsGraphUp: IconType;
export declare const BsGrid1X2Fill: IconType;
export declare const BsGrid1X2: IconType;
export declare const BsGrid3X2GapFill: IconType;
export declare const BsGrid3X2Gap: IconType;
export declare const BsGrid3X2: IconType;
export declare const BsGrid3X3GapFill: IconType;
export declare const BsGrid3X3Gap: IconType;
export declare const BsGrid3X3: IconType;
export declare const BsGridFill: IconType;
export declare const BsGrid: IconType;
export declare const BsGripHorizontal: IconType;
export declare const BsGripVertical: IconType;
export declare const BsHCircleFill: IconType;
export declare const BsHCircle: IconType;
export declare const BsHSquareFill: IconType;
export declare const BsHSquare: IconType;
export declare const BsHammer: IconType;
export declare const BsHandIndexFill: IconType;
export declare const BsHandIndexThumbFill: IconType;
export declare const BsHandIndexThumb: IconType;
export declare const BsHandIndex: IconType;
export declare const BsHandThumbsDownFill: IconType;
export declare const BsHandThumbsDown: IconType;
export declare const BsHandThumbsUpFill: IconType;
export declare const BsHandThumbsUp: IconType;
export declare const BsHandbagFill: IconType;
export declare const BsHandbag: IconType;
export declare const BsHash: IconType;
export declare const BsHddFill: IconType;
export declare const BsHddNetworkFill: IconType;
export declare const BsHddNetwork: IconType;
export declare const BsHddRackFill: IconType;
export declare const BsHddRack: IconType;
export declare const BsHddStackFill: IconType;
export declare const BsHddStack: IconType;
export declare const BsHdd: IconType;
export declare const BsHdmiFill: IconType;
export declare const BsHdmi: IconType;
export declare const BsHeadphones: IconType;
export declare const BsHeadsetVr: IconType;
export declare const BsHeadset: IconType;
export declare const BsHeartArrow: IconType;
export declare const BsHeartFill: IconType;
export declare const BsHeartHalf: IconType;
export declare const BsHeartPulseFill: IconType;
export declare const BsHeartPulse: IconType;
export declare const BsHeart: IconType;
export declare const BsHeartbreakFill: IconType;
export declare const BsHeartbreak: IconType;
export declare const BsHearts: IconType;
export declare const BsHeptagonFill: IconType;
export declare const BsHeptagonHalf: IconType;
export declare const BsHeptagon: IconType;
export declare const BsHexagonFill: IconType;
export declare const BsHexagonHalf: IconType;
export declare const BsHexagon: IconType;
export declare const BsHighlighter: IconType;
export declare const BsHighlights: IconType;
export declare const BsHospitalFill: IconType;
export declare const BsHospital: IconType;
export declare const BsHourglassBottom: IconType;
export declare const BsHourglassSplit: IconType;
export declare const BsHourglassTop: IconType;
export declare const BsHourglass: IconType;
export declare const BsHouseAddFill: IconType;
export declare const BsHouseAdd: IconType;
export declare const BsHouseCheckFill: IconType;
export declare const BsHouseCheck: IconType;
export declare const BsHouseDashFill: IconType;
export declare const BsHouseDash: IconType;
export declare const BsHouseDoorFill: IconType;
export declare const BsHouseDoor: IconType;
export declare const BsHouseDownFill: IconType;
export declare const BsHouseDown: IconType;
export declare const BsHouseExclamationFill: IconType;
export declare const BsHouseExclamation: IconType;
export declare const BsHouseFill: IconType;
export declare const BsHouseGearFill: IconType;
export declare const BsHouseGear: IconType;
export declare const BsHouseHeartFill: IconType;
export declare const BsHouseHeart: IconType;
export declare const BsHouseLockFill: IconType;
export declare const BsHouseLock: IconType;
export declare const BsHouseSlashFill: IconType;
export declare const BsHouseSlash: IconType;
export declare const BsHouseUpFill: IconType;
export declare const BsHouseUp: IconType;
export declare const BsHouseXFill: IconType;
export declare const BsHouseX: IconType;
export declare const BsHouse: IconType;
export declare const BsHousesFill: IconType;
export declare const BsHouses: IconType;
export declare const BsHr: IconType;
export declare const BsHurricane: IconType;
export declare const BsHypnotize: IconType;
export declare const BsImageAlt: IconType;
export declare const BsImageFill: IconType;
export declare const BsImage: IconType;
export declare const BsImages: IconType;
export declare const BsInboxFill: IconType;
export declare const BsInbox: IconType;
export declare const BsInboxesFill: IconType;
export declare const BsInboxes: IconType;
export declare const BsIncognito: IconType;
export declare const BsIndent: IconType;
export declare const BsInfinity: IconType;
export declare const BsInfoCircleFill: IconType;
export declare const BsInfoCircle: IconType;
export declare const BsInfoLg: IconType;
export declare const BsInfoSquareFill: IconType;
export declare const BsInfoSquare: IconType;
export declare const BsInfo: IconType;
export declare const BsInputCursorText: IconType;
export declare const BsInputCursor: IconType;
export declare const BsInstagram: IconType;
export declare const BsIntersect: IconType;
export declare const BsJournalAlbum: IconType;
export declare const BsJournalArrowDown: IconType;
export declare const BsJournalArrowUp: IconType;
export declare const BsJournalBookmarkFill: IconType;
export declare const BsJournalBookmark: IconType;
export declare const BsJournalCheck: IconType;
export declare const BsJournalCode: IconType;
export declare const BsJournalMedical: IconType;
export declare const BsJournalMinus: IconType;
export declare const BsJournalPlus: IconType;
export declare const BsJournalRichtext: IconType;
export declare const BsJournalText: IconType;
export declare const BsJournalX: IconType;
export declare const BsJournal: IconType;
export declare const BsJournals: IconType;
export declare const BsJoystick: IconType;
export declare const BsJustifyLeft: IconType;
export declare const BsJustifyRight: IconType;
export declare const BsJustify: IconType;
export declare const BsKanbanFill: IconType;
export declare const BsKanban: IconType;
export declare const BsKeyFill: IconType;
export declare const BsKey: IconType;
export declare const BsKeyboardFill: IconType;
export declare const BsKeyboard: IconType;
export declare const BsLadder: IconType;
export declare const BsLampFill: IconType;
export declare const BsLamp: IconType;
export declare const BsLaptopFill: IconType;
export declare const BsLaptop: IconType;
export declare const BsLayerBackward: IconType;
export declare const BsLayerForward: IconType;
export declare const BsLayersFill: IconType;
export declare const BsLayersHalf: IconType;
export declare const BsLayers: IconType;
export declare const BsLayoutSidebarInsetReverse: IconType;
export declare const BsLayoutSidebarInset: IconType;
export declare const BsLayoutSidebarReverse: IconType;
export declare const BsLayoutSidebar: IconType;
export declare const BsLayoutSplit: IconType;
export declare const BsLayoutTextSidebarReverse: IconType;
export declare const BsLayoutTextSidebar: IconType;
export declare const BsLayoutTextWindowReverse: IconType;
export declare const BsLayoutTextWindow: IconType;
export declare const BsLayoutThreeColumns: IconType;
export declare const BsLayoutWtf: IconType;
export declare const BsLifePreserver: IconType;
export declare const BsLightbulbFill: IconType;
export declare const BsLightbulbOffFill: IconType;
export declare const BsLightbulbOff: IconType;
export declare const BsLightbulb: IconType;
export declare const BsLightningChargeFill: IconType;
export declare const BsLightningCharge: IconType;
export declare const BsLightningFill: IconType;
export declare const BsLightning: IconType;
export declare const BsLine: IconType;
export declare const BsLink45Deg: IconType;
export declare const BsLink: IconType;
export declare const BsLinkedin: IconType;
export declare const BsListCheck: IconType;
export declare const BsListColumnsReverse: IconType;
export declare const BsListColumns: IconType;
export declare const BsListNested: IconType;
export declare const BsListOl: IconType;
export declare const BsListStars: IconType;
export declare const BsListTask: IconType;
export declare const BsListUl: IconType;
export declare const BsList: IconType;
export declare const BsLockFill: IconType;
export declare const BsLock: IconType;
export declare const BsLuggageFill: IconType;
export declare const BsLuggage: IconType;
export declare const BsLungsFill: IconType;
export declare const BsLungs: IconType;
export declare const BsMagic: IconType;
export declare const BsMagnetFill: IconType;
export declare const BsMagnet: IconType;
export declare const BsMailboxFlag: IconType;
export declare const BsMailbox: IconType;
export declare const BsMailbox2Flag: IconType;
export declare const BsMailbox2: IconType;
export declare const BsMapFill: IconType;
export declare const BsMap: IconType;
export declare const BsMarkdownFill: IconType;
export declare const BsMarkdown: IconType;
export declare const BsMarkerTip: IconType;
export declare const BsMask: IconType;
export declare const BsMastodon: IconType;
export declare const BsMedium: IconType;
export declare const BsMegaphoneFill: IconType;
export declare const BsMegaphone: IconType;
export declare const BsMemory: IconType;
export declare const BsMenuAppFill: IconType;
export declare const BsMenuApp: IconType;
export declare const BsMenuButtonFill: IconType;
export declare const BsMenuButtonWideFill: IconType;
export declare const BsMenuButtonWide: IconType;
export declare const BsMenuButton: IconType;
export declare const BsMenuDown: IconType;
export declare const BsMenuUp: IconType;
export declare const BsMessenger: IconType;
export declare const BsMeta: IconType;
export declare const BsMicFill: IconType;
export declare const BsMicMuteFill: IconType;
export declare const BsMicMute: IconType;
export declare const BsMic: IconType;
export declare const BsMicrosoftTeams: IconType;
export declare const BsMicrosoft: IconType;
export declare const BsMinecartLoaded: IconType;
export declare const BsMinecart: IconType;
export declare const BsModemFill: IconType;
export declare const BsModem: IconType;
export declare const BsMoisture: IconType;
export declare const BsMoonFill: IconType;
export declare const BsMoonStarsFill: IconType;
export declare const BsMoonStars: IconType;
export declare const BsMoon: IconType;
export declare const BsMortarboardFill: IconType;
export declare const BsMortarboard: IconType;
export declare const BsMotherboardFill: IconType;
export declare const BsMotherboard: IconType;
export declare const BsMouseFill: IconType;
export declare const BsMouse: IconType;
export declare const BsMouse2Fill: IconType;
export declare const BsMouse2: IconType;
export declare const BsMouse3Fill: IconType;
export declare const BsMouse3: IconType;
export declare const BsMusicNoteBeamed: IconType;
export declare const BsMusicNoteList: IconType;
export declare const BsMusicNote: IconType;
export declare const BsMusicPlayerFill: IconType;
export declare const BsMusicPlayer: IconType;
export declare const BsNewspaper: IconType;
export declare const BsNintendoSwitch: IconType;
export declare const BsNodeMinusFill: IconType;
export declare const BsNodeMinus: IconType;
export declare const BsNodePlusFill: IconType;
export declare const BsNodePlus: IconType;
export declare const BsNoiseReduction: IconType;
export declare const BsNutFill: IconType;
export declare const BsNut: IconType;
export declare const BsNvidia: IconType;
export declare const BsNvmeFill: IconType;
export declare const BsNvme: IconType;
export declare const BsOctagonFill: IconType;
export declare const BsOctagonHalf: IconType;
export declare const BsOctagon: IconType;
export declare const BsOpencollective: IconType;
export declare const BsOpticalAudioFill: IconType;
export declare const BsOpticalAudio: IconType;
export declare const BsOption: IconType;
export declare const BsOutlet: IconType;
export declare const BsPCircleFill: IconType;
export declare const BsPCircle: IconType;
export declare const BsPSquareFill: IconType;
export declare const BsPSquare: IconType;
export declare const BsPaintBucket: IconType;
export declare const BsPaletteFill: IconType;
export declare const BsPalette: IconType;
export declare const BsPalette2: IconType;
export declare const BsPaperclip: IconType;
export declare const BsParagraph: IconType;
export declare const BsPassFill: IconType;
export declare const BsPass: IconType;
export declare const BsPassportFill: IconType;
export declare const BsPassport: IconType;
export declare const BsPatchCheckFill: IconType;
export declare const BsPatchCheck: IconType;
export declare const BsPatchExclamationFill: IconType;
export declare const BsPatchExclamation: IconType;
export declare const BsPatchMinusFill: IconType;
export declare const BsPatchMinus: IconType;
export declare const BsPatchPlusFill: IconType;
export declare const BsPatchPlus: IconType;
export declare const BsPatchQuestionFill: IconType;
export declare const BsPatchQuestion: IconType;
export declare const BsPauseBtnFill: IconType;
export declare const BsPauseBtn: IconType;
export declare const BsPauseCircleFill: IconType;
export declare const BsPauseCircle: IconType;
export declare const BsPauseFill: IconType;
export declare const BsPause: IconType;
export declare const BsPaypal: IconType;
export declare const BsPcDisplayHorizontal: IconType;
export declare const BsPcDisplay: IconType;
export declare const BsPcHorizontal: IconType;
export declare const BsPc: IconType;
export declare const BsPciCardNetwork: IconType;
export declare const BsPciCardSound: IconType;
export declare const BsPciCard: IconType;
export declare const BsPeaceFill: IconType;
export declare const BsPeace: IconType;
export declare const BsPenFill: IconType;
export declare const BsPen: IconType;
export declare const BsPencilFill: IconType;
export declare const BsPencilSquare: IconType;
export declare const BsPencil: IconType;
export declare const BsPentagonFill: IconType;
export declare const BsPentagonHalf: IconType;
export declare const BsPentagon: IconType;
export declare const BsPeopleFill: IconType;
export declare const BsPeople: IconType;
export declare const BsPercent: IconType;
export declare const BsPersonAdd: IconType;
export declare const BsPersonArmsUp: IconType;
export declare const BsPersonBadgeFill: IconType;
export declare const BsPersonBadge: IconType;
export declare const BsPersonBoundingBox: IconType;
export declare const BsPersonCheckFill: IconType;
export declare const BsPersonCheck: IconType;
export declare const BsPersonCircle: IconType;
export declare const BsPersonDashFill: IconType;
export declare const BsPersonDash: IconType;
export declare const BsPersonDown: IconType;
export declare const BsPersonExclamation: IconType;
export declare const BsPersonFillAdd: IconType;
export declare const BsPersonFillCheck: IconType;
export declare const BsPersonFillDash: IconType;
export declare const BsPersonFillDown: IconType;
export declare const BsPersonFillExclamation: IconType;
export declare const BsPersonFillGear: IconType;
export declare const BsPersonFillLock: IconType;
export declare const BsPersonFillSlash: IconType;
export declare const BsPersonFillUp: IconType;
export declare const BsPersonFillX: IconType;
export declare const BsPersonFill: IconType;
export declare const BsPersonGear: IconType;
export declare const BsPersonHeart: IconType;
export declare const BsPersonHearts: IconType;
export declare const BsPersonLinesFill: IconType;
export declare const BsPersonLock: IconType;
export declare const BsPersonPlusFill: IconType;
export declare const BsPersonPlus: IconType;
export declare const BsPersonRaisedHand: IconType;
export declare const BsPersonRolodex: IconType;
export declare const BsPersonSlash: IconType;
export declare const BsPersonSquare: IconType;
export declare const BsPersonStandingDress: IconType;
export declare const BsPersonStanding: IconType;
export declare const BsPersonUp: IconType;
export declare const BsPersonVcardFill: IconType;
export declare const BsPersonVcard: IconType;
export declare const BsPersonVideo: IconType;
export declare const BsPersonVideo2: IconType;
export declare const BsPersonVideo3: IconType;
export declare const BsPersonWalking: IconType;
export declare const BsPersonWheelchair: IconType;
export declare const BsPersonWorkspace: IconType;
export declare const BsPersonXFill: IconType;
export declare const BsPersonX: IconType;
export declare const BsPerson: IconType;
export declare const BsPhoneFill: IconType;
export declare const BsPhoneFlip: IconType;
export declare const BsPhoneLandscapeFill: IconType;
export declare const BsPhoneLandscape: IconType;
export declare const BsPhoneVibrateFill: IconType;
export declare const BsPhoneVibrate: IconType;
export declare const BsPhone: IconType;
export declare const BsPieChartFill: IconType;
export declare const BsPieChart: IconType;
export declare const BsPiggyBankFill: IconType;
export declare const BsPiggyBank: IconType;
export declare const BsPinAngleFill: IconType;
export declare const BsPinAngle: IconType;
export declare const BsPinFill: IconType;
export declare const BsPinMapFill: IconType;
export declare const BsPinMap: IconType;
export declare const BsPin: IconType;
export declare const BsPinterest: IconType;
export declare const BsPipFill: IconType;
export declare const BsPip: IconType;
export declare const BsPlayBtnFill: IconType;
export declare const BsPlayBtn: IconType;
export declare const BsPlayCircleFill: IconType;
export declare const BsPlayCircle: IconType;
export declare const BsPlayFill: IconType;
export declare const BsPlay: IconType;
export declare const BsPlaystation: IconType;
export declare const BsPlugFill: IconType;
export declare const BsPlug: IconType;
export declare const BsPlugin: IconType;
export declare const BsPlusCircleDotted: IconType;
export declare const BsPlusCircleFill: IconType;
export declare const BsPlusCircle: IconType;
export declare const BsPlusLg: IconType;
export declare const BsPlusSlashMinus: IconType;
export declare const BsPlusSquareDotted: IconType;
export declare const BsPlusSquareFill: IconType;
export declare const BsPlusSquare: IconType;
export declare const BsPlus: IconType;
export declare const BsPostageFill: IconType;
export declare const BsPostageHeartFill: IconType;
export declare const BsPostageHeart: IconType;
export declare const BsPostage: IconType;
export declare const BsPostcardFill: IconType;
export declare const BsPostcardHeartFill: IconType;
export declare const BsPostcardHeart: IconType;
export declare const BsPostcard: IconType;
export declare const BsPower: IconType;
export declare const BsPrescription: IconType;
export declare const BsPrescription2: IconType;
export declare const BsPrinterFill: IconType;
export declare const BsPrinter: IconType;
export declare const BsProjectorFill: IconType;
export declare const BsProjector: IconType;
export declare const BsPuzzleFill: IconType;
export declare const BsPuzzle: IconType;
export declare const BsQrCodeScan: IconType;
export declare const BsQrCode: IconType;
export declare const BsQuestionCircleFill: IconType;
export declare const BsQuestionCircle: IconType;
export declare const BsQuestionDiamondFill: IconType;
export declare const BsQuestionDiamond: IconType;
export declare const BsQuestionLg: IconType;
export declare const BsQuestionOctagonFill: IconType;
export declare const BsQuestionOctagon: IconType;
export declare const BsQuestionSquareFill: IconType;
export declare const BsQuestionSquare: IconType;
export declare const BsQuestion: IconType;
export declare const BsQuora: IconType;
export declare const BsQuote: IconType;
export declare const BsRCircleFill: IconType;
export declare const BsRCircle: IconType;
export declare const BsRSquareFill: IconType;
export declare const BsRSquare: IconType;
export declare const BsRadar: IconType;
export declare const BsRadioactive: IconType;
export declare const BsRainbow: IconType;
export declare const BsReceiptCutoff: IconType;
export declare const BsReceipt: IconType;
export declare const BsReception0: IconType;
export declare const BsReception1: IconType;
export declare const BsReception2: IconType;
export declare const BsReception3: IconType;
export declare const BsReception4: IconType;
export declare const BsRecordBtnFill: IconType;
export declare const BsRecordBtn: IconType;
export declare const BsRecordCircleFill: IconType;
export declare const BsRecordCircle: IconType;
export declare const BsRecordFill: IconType;
export declare const BsRecord: IconType;
export declare const BsRecord2Fill: IconType;
export declare const BsRecord2: IconType;
export declare const BsRecycle: IconType;
export declare const BsReddit: IconType;
export declare const BsRegex: IconType;
export declare const BsRepeat1: IconType;
export declare const BsRepeat: IconType;
export declare const BsReplyAllFill: IconType;
export declare const BsReplyAll: IconType;
export declare const BsReplyFill: IconType;
export declare const BsReply: IconType;
export declare const BsRewindBtnFill: IconType;
export declare const BsRewindBtn: IconType;
export declare const BsRewindCircleFill: IconType;
export declare const BsRewindCircle: IconType;
export declare const BsRewindFill: IconType;
export declare const BsRewind: IconType;
export declare const BsRobot: IconType;
export declare const BsRocketFill: IconType;
export declare const BsRocketTakeoffFill: IconType;
export declare const BsRocketTakeoff: IconType;
export declare const BsRocket: IconType;
export declare const BsRouterFill: IconType;
export declare const BsRouter: IconType;
export declare const BsRssFill: IconType;
export declare const BsRss: IconType;
export declare const BsRulers: IconType;
export declare const BsSafeFill: IconType;
export declare const BsSafe: IconType;
export declare const BsSafe2Fill: IconType;
export declare const BsSafe2: IconType;
export declare const BsSaveFill: IconType;
export declare const BsSave: IconType;
export declare const BsSave2Fill: IconType;
export declare const BsSave2: IconType;
export declare const BsScissors: IconType;
export declare const BsScooter: IconType;
export declare const BsScrewdriver: IconType;
export declare const BsSdCardFill: IconType;
export declare const BsSdCard: IconType;
export declare const BsSearchHeartFill: IconType;
export declare const BsSearchHeart: IconType;
export declare const BsSearch: IconType;
export declare const BsSegmentedNav: IconType;
export declare const BsSendArrowDownFill: IconType;
export declare const BsSendArrowDown: IconType;
export declare const BsSendArrowUpFill: IconType;
export declare const BsSendArrowUp: IconType;
export declare const BsSendCheckFill: IconType;
export declare const BsSendCheck: IconType;
export declare const BsSendDashFill: IconType;
export declare const BsSendDash: IconType;
export declare const BsSendExclamationFill: IconType;
export declare const BsSendExclamation: IconType;
export declare const BsSendFill: IconType;
export declare const BsSendPlusFill: IconType;
export declare const BsSendPlus: IconType;
export declare const BsSendSlashFill: IconType;
export declare const BsSendSlash: IconType;
export declare const BsSendXFill: IconType;
export declare const BsSendX: IconType;
export declare const BsSend: IconType;
export declare const BsServer: IconType;
export declare const BsShadows: IconType;
export declare const BsShareFill: IconType;
export declare const BsShare: IconType;
export declare const BsShieldCheck: IconType;
export declare const BsShieldExclamation: IconType;
export declare const BsShieldFillCheck: IconType;
export declare const BsShieldFillExclamation: IconType;
export declare const BsShieldFillMinus: IconType;
export declare const BsShieldFillPlus: IconType;
export declare const BsShieldFillX: IconType;
export declare const BsShieldFill: IconType;
export declare const BsShieldLockFill: IconType;
export declare const BsShieldLock: IconType;
export declare const BsShieldMinus: IconType;
export declare const BsShieldPlus: IconType;
export declare const BsShieldShaded: IconType;
export declare const BsShieldSlashFill: IconType;
export declare const BsShieldSlash: IconType;
export declare const BsShieldX: IconType;
export declare const BsShield: IconType;
export declare const BsShiftFill: IconType;
export declare const BsShift: IconType;
export declare const BsShopWindow: IconType;
export declare const BsShop: IconType;
export declare const BsShuffle: IconType;
export declare const BsSignDeadEndFill: IconType;
export declare const BsSignDeadEnd: IconType;
export declare const BsSignDoNotEnterFill: IconType;
export declare const BsSignDoNotEnter: IconType;
export declare const BsSignIntersectionFill: IconType;
export declare const BsSignIntersectionSideFill: IconType;
export declare const BsSignIntersectionSide: IconType;
export declare const BsSignIntersectionTFill: IconType;
export declare const BsSignIntersectionT: IconType;
export declare const BsSignIntersectionYFill: IconType;
export declare const BsSignIntersectionY: IconType;
export declare const BsSignIntersection: IconType;
export declare const BsSignMergeLeftFill: IconType;
export declare const BsSignMergeLeft: IconType;
export declare const BsSignMergeRightFill: IconType;
export declare const BsSignMergeRight: IconType;
export declare const BsSignNoLeftTurnFill: IconType;
export declare const BsSignNoLeftTurn: IconType;
export declare const BsSignNoParkingFill: IconType;
export declare const BsSignNoParking: IconType;
export declare const BsSignNoRightTurnFill: IconType;
export declare const BsSignNoRightTurn: IconType;
export declare const BsSignRailroadFill: IconType;
export declare const BsSignRailroad: IconType;
export declare const BsSignStopFill: IconType;
export declare const BsSignStopLightsFill: IconType;
export declare const BsSignStopLights: IconType;
export declare const BsSignStop: IconType;
export declare const BsSignTurnLeftFill: IconType;
export declare const BsSignTurnLeft: IconType;
export declare const BsSignTurnRightFill: IconType;
export declare const BsSignTurnRight: IconType;
export declare const BsSignTurnSlightLeftFill: IconType;
export declare const BsSignTurnSlightLeft: IconType;
export declare const BsSignTurnSlightRightFill: IconType;
export declare const BsSignTurnSlightRight: IconType;
export declare const BsSignYieldFill: IconType;
export declare const BsSignYield: IconType;
export declare const BsSignal: IconType;
export declare const BsSignpost2Fill: IconType;
export declare const BsSignpost2: IconType;
export declare const BsSignpostFill: IconType;
export declare const BsSignpostSplitFill: IconType;
export declare const BsSignpostSplit: IconType;
export declare const BsSignpost: IconType;
export declare const BsSimFill: IconType;
export declare const BsSimSlashFill: IconType;
export declare const BsSimSlash: IconType;
export declare const BsSim: IconType;
export declare const BsSinaWeibo: IconType;
export declare const BsSkipBackwardBtnFill: IconType;
export declare const BsSkipBackwardBtn: IconType;
export declare const BsSkipBackwardCircleFill: IconType;
export declare const BsSkipBackwardCircle: IconType;
export declare const BsSkipBackwardFill: IconType;
export declare const BsSkipBackward: IconType;
export declare const BsSkipEndBtnFill: IconType;
export declare const BsSkipEndBtn: IconType;
export declare const BsSkipEndCircleFill: IconType;
export declare const BsSkipEndCircle: IconType;
export declare const BsSkipEndFill: IconType;
export declare const BsSkipEnd: IconType;
export declare const BsSkipForwardBtnFill: IconType;
export declare const BsSkipForwardBtn: IconType;
export declare const BsSkipForwardCircleFill: IconType;
export declare const BsSkipForwardCircle: IconType;
export declare const BsSkipForwardFill: IconType;
export declare const BsSkipForward: IconType;
export declare const BsSkipStartBtnFill: IconType;
export declare const BsSkipStartBtn: IconType;
export declare const BsSkipStartCircleFill: IconType;
export declare const BsSkipStartCircle: IconType;
export declare const BsSkipStartFill: IconType;
export declare const BsSkipStart: IconType;
export declare const BsSkype: IconType;
export declare const BsSlack: IconType;
export declare const BsSlashCircleFill: IconType;
export declare const BsSlashCircle: IconType;
export declare const BsSlashLg: IconType;
export declare const BsSlashSquareFill: IconType;
export declare const BsSlashSquare: IconType;
export declare const BsSlash: IconType;
export declare const BsSliders: IconType;
export declare const BsSliders2Vertical: IconType;
export declare const BsSliders2: IconType;
export declare const BsSmartwatch: IconType;
export declare const BsSnapchat: IconType;
export declare const BsSnow: IconType;
export declare const BsSnow2: IconType;
export declare const BsSnow3: IconType;
export declare const BsSortAlphaDownAlt: IconType;
export declare const BsSortAlphaDown: IconType;
export declare const BsSortAlphaUpAlt: IconType;
export declare const BsSortAlphaUp: IconType;
export declare const BsSortDownAlt: IconType;
export declare const BsSortDown: IconType;
export declare const BsSortNumericDownAlt: IconType;
export declare const BsSortNumericDown: IconType;
export declare const BsSortNumericUpAlt: IconType;
export declare const BsSortNumericUp: IconType;
export declare const BsSortUpAlt: IconType;
export declare const BsSortUp: IconType;
export declare const BsSoundwave: IconType;
export declare const BsSourceforge: IconType;
export declare const BsSpeakerFill: IconType;
export declare const BsSpeaker: IconType;
export declare const BsSpeedometer: IconType;
export declare const BsSpeedometer2: IconType;
export declare const BsSpellcheck: IconType;
export declare const BsSpotify: IconType;
export declare const BsSquareFill: IconType;
export declare const BsSquareHalf: IconType;
export declare const BsSquare: IconType;
export declare const BsStackOverflow: IconType;
export declare const BsStack: IconType;
export declare const BsStarFill: IconType;
export declare const BsStarHalf: IconType;
export declare const BsStar: IconType;
export declare const BsStars: IconType;
export declare const BsSteam: IconType;
export declare const BsStickiesFill: IconType;
export declare const BsStickies: IconType;
export declare const BsStickyFill: IconType;
export declare const BsSticky: IconType;
export declare const BsStopBtnFill: IconType;
export declare const BsStopBtn: IconType;
export declare const BsStopCircleFill: IconType;
export declare const BsStopCircle: IconType;
export declare const BsStopFill: IconType;
export declare const BsStop: IconType;
export declare const BsStoplightsFill: IconType;
export declare const BsStoplights: IconType;
export declare const BsStopwatchFill: IconType;
export declare const BsStopwatch: IconType;
export declare const BsStrava: IconType;
export declare const BsStripe: IconType;
export declare const BsSubscript: IconType;
export declare const BsSubstack: IconType;
export declare const BsSubtract: IconType;
export declare const BsSuitClubFill: IconType;
export declare const BsSuitClub: IconType;
export declare const BsSuitDiamondFill: IconType;
export declare const BsSuitDiamond: IconType;
export declare const BsSuitHeartFill: IconType;
export declare const BsSuitHeart: IconType;
export declare const BsSuitSpadeFill: IconType;
export declare const BsSuitSpade: IconType;
export declare const BsSuitcaseFill: IconType;
export declare const BsSuitcaseLgFill: IconType;
export declare const BsSuitcaseLg: IconType;
export declare const BsSuitcase: IconType;
export declare const BsSuitcase2Fill: IconType;
export declare const BsSuitcase2: IconType;
export declare const BsSunFill: IconType;
export declare const BsSun: IconType;
export declare const BsSunglasses: IconType;
export declare const BsSunriseFill: IconType;
export declare const BsSunrise: IconType;
export declare const BsSunsetFill: IconType;
export declare const BsSunset: IconType;
export declare const BsSuperscript: IconType;
export declare const BsSymmetryHorizontal: IconType;
export declare const BsSymmetryVertical: IconType;
export declare const BsTable: IconType;
export declare const BsTabletFill: IconType;
export declare const BsTabletLandscapeFill: IconType;
export declare const BsTabletLandscape: IconType;
export declare const BsTablet: IconType;
export declare const BsTagFill: IconType;
export declare const BsTag: IconType;
export declare const BsTagsFill: IconType;
export declare const BsTags: IconType;
export declare const BsTaxiFrontFill: IconType;
export declare const BsTaxiFront: IconType;
export declare const BsTelegram: IconType;
export declare const BsTelephoneFill: IconType;
export declare const BsTelephoneForwardFill: IconType;
export declare const BsTelephoneForward: IconType;
export declare const BsTelephoneInboundFill: IconType;
export declare const BsTelephoneInbound: IconType;
export declare const BsTelephoneMinusFill: IconType;
export declare const BsTelephoneMinus: IconType;
export declare const BsTelephoneOutboundFill: IconType;
export declare const BsTelephoneOutbound: IconType;
export declare const BsTelephonePlusFill: IconType;
export declare const BsTelephonePlus: IconType;
export declare const BsTelephoneXFill: IconType;
export declare const BsTelephoneX: IconType;
export declare const BsTelephone: IconType;
export declare const BsTencentQq: IconType;
export declare const BsTerminalDash: IconType;
export declare const BsTerminalFill: IconType;
export declare const BsTerminalPlus: IconType;
export declare const BsTerminalSplit: IconType;
export declare const BsTerminalX: IconType;
export declare const BsTerminal: IconType;
export declare const BsTextCenter: IconType;
export declare const BsTextIndentLeft: IconType;
export declare const BsTextIndentRight: IconType;
export declare const BsTextLeft: IconType;
export declare const BsTextParagraph: IconType;
export declare const BsTextRight: IconType;
export declare const BsTextWrap: IconType;
export declare const BsTextareaResize: IconType;
export declare const BsTextareaT: IconType;
export declare const BsTextarea: IconType;
export declare const BsThermometerHalf: IconType;
export declare const BsThermometerHigh: IconType;
export declare const BsThermometerLow: IconType;
export declare const BsThermometerSnow: IconType;
export declare const BsThermometerSun: IconType;
export declare const BsThermometer: IconType;
export declare const BsThreadsFill: IconType;
export declare const BsThreads: IconType;
export declare const BsThreeDotsVertical: IconType;
export declare const BsThreeDots: IconType;
export declare const BsThunderboltFill: IconType;
export declare const BsThunderbolt: IconType;
export declare const BsTicketDetailedFill: IconType;
export declare const BsTicketDetailed: IconType;
export declare const BsTicketFill: IconType;
export declare const BsTicketPerforatedFill: IconType;
export declare const BsTicketPerforated: IconType;
export declare const BsTicket: IconType;
export declare const BsTiktok: IconType;
export declare const BsToggleOff: IconType;
export declare const BsToggleOn: IconType;
export declare const BsToggle2Off: IconType;
export declare const BsToggle2On: IconType;
export declare const BsToggles: IconType;
export declare const BsToggles2: IconType;
export declare const BsTools: IconType;
export declare const BsTornado: IconType;
export declare const BsTrainFreightFrontFill: IconType;
export declare const BsTrainFreightFront: IconType;
export declare const BsTrainFrontFill: IconType;
export declare const BsTrainFront: IconType;
export declare const BsTrainLightrailFrontFill: IconType;
export declare const BsTrainLightrailFront: IconType;
export declare const BsTranslate: IconType;
export declare const BsTransparency: IconType;
export declare const BsTrashFill: IconType;
export declare const BsTrash: IconType;
export declare const BsTrash2Fill: IconType;
export declare const BsTrash2: IconType;
export declare const BsTrash3Fill: IconType;
export declare const BsTrash3: IconType;
export declare const BsTreeFill: IconType;
export declare const BsTree: IconType;
export declare const BsTrello: IconType;
export declare const BsTriangleFill: IconType;
export declare const BsTriangleHalf: IconType;
export declare const BsTriangle: IconType;
export declare const BsTrophyFill: IconType;
export declare const BsTrophy: IconType;
export declare const BsTropicalStorm: IconType;
export declare const BsTruckFlatbed: IconType;
export declare const BsTruckFrontFill: IconType;
export declare const BsTruckFront: IconType;
export declare const BsTruck: IconType;
export declare const BsTsunami: IconType;
export declare const BsTvFill: IconType;
export declare const BsTv: IconType;
export declare const BsTwitch: IconType;
export declare const BsTwitterX: IconType;
export declare const BsTwitter: IconType;
export declare const BsTypeBold: IconType;
export declare const BsTypeH1: IconType;
export declare const BsTypeH2: IconType;
export declare const BsTypeH3: IconType;
export declare const BsTypeH4: IconType;
export declare const BsTypeH5: IconType;
export declare const BsTypeH6: IconType;
export declare const BsTypeItalic: IconType;
export declare const BsTypeStrikethrough: IconType;
export declare const BsTypeUnderline: IconType;
export declare const BsType: IconType;
export declare const BsUbuntu: IconType;
export declare const BsUiChecksGrid: IconType;
export declare const BsUiChecks: IconType;
export declare const BsUiRadiosGrid: IconType;
export declare const BsUiRadios: IconType;
export declare const BsUmbrellaFill: IconType;
export declare const BsUmbrella: IconType;
export declare const BsUnindent: IconType;
export declare const BsUnion: IconType;
export declare const BsUnity: IconType;
export declare const BsUniversalAccessCircle: IconType;
export declare const BsUniversalAccess: IconType;
export declare const BsUnlockFill: IconType;
export declare const BsUnlock: IconType;
export declare const BsUpcScan: IconType;
export declare const BsUpc: IconType;
export declare const BsUpload: IconType;
export declare const BsUsbCFill: IconType;
export declare const BsUsbC: IconType;
export declare const BsUsbDriveFill: IconType;
export declare const BsUsbDrive: IconType;
export declare const BsUsbFill: IconType;
export declare const BsUsbMicroFill: IconType;
export declare const BsUsbMicro: IconType;
export declare const BsUsbMiniFill: IconType;
export declare const BsUsbMini: IconType;
export declare const BsUsbPlugFill: IconType;
export declare const BsUsbPlug: IconType;
export declare const BsUsbSymbol: IconType;
export declare const BsUsb: IconType;
export declare const BsValentine: IconType;
export declare const BsValentine2: IconType;
export declare const BsVectorPen: IconType;
export declare const BsViewList: IconType;
export declare const BsViewStacked: IconType;
export declare const BsVignette: IconType;
export declare const BsVimeo: IconType;
export declare const BsVinylFill: IconType;
export declare const BsVinyl: IconType;
export declare const BsVirus: IconType;
export declare const BsVirus2: IconType;
export declare const BsVoicemail: IconType;
export declare const BsVolumeDownFill: IconType;
export declare const BsVolumeDown: IconType;
export declare const BsVolumeMuteFill: IconType;
export declare const BsVolumeMute: IconType;
export declare const BsVolumeOffFill: IconType;
export declare const BsVolumeOff: IconType;
export declare const BsVolumeUpFill: IconType;
export declare const BsVolumeUp: IconType;
export declare const BsVr: IconType;
export declare const BsWalletFill: IconType;
export declare const BsWallet: IconType;
export declare const BsWallet2: IconType;
export declare const BsWatch: IconType;
export declare const BsWater: IconType;
export declare const BsWebcamFill: IconType;
export declare const BsWebcam: IconType;
export declare const BsWechat: IconType;
export declare const BsWhatsapp: IconType;
export declare const BsWifi1: IconType;
export declare const BsWifi2: IconType;
export declare const BsWifiOff: IconType;
export declare const BsWifi: IconType;
export declare const BsWikipedia: IconType;
export declare const BsWind: IconType;
export declare const BsWindowDash: IconType;
export declare const BsWindowDesktop: IconType;
export declare const BsWindowDock: IconType;
export declare const BsWindowFullscreen: IconType;
export declare const BsWindowPlus: IconType;
export declare const BsWindowSidebar: IconType;
export declare const BsWindowSplit: IconType;
export declare const BsWindowStack: IconType;
export declare const BsWindowX: IconType;
export declare const BsWindow: IconType;
export declare const BsWindows: IconType;
export declare const BsWordpress: IconType;
export declare const BsWrenchAdjustableCircleFill: IconType;
export declare const BsWrenchAdjustableCircle: IconType;
export declare const BsWrenchAdjustable: IconType;
export declare const BsWrench: IconType;
export declare const BsXCircleFill: IconType;
export declare const BsXCircle: IconType;
export declare const BsXDiamondFill: IconType;
export declare const BsXDiamond: IconType;
export declare const BsXLg: IconType;
export declare const BsXOctagonFill: IconType;
export declare const BsXOctagon: IconType;
export declare const BsXSquareFill: IconType;
export declare const BsXSquare: IconType;
export declare const BsX: IconType;
export declare const BsXbox: IconType;
export declare const BsYelp: IconType;
export declare const BsYinYang: IconType;
export declare const BsYoutube: IconType;
export declare const BsZoomIn: IconType;
export declare const BsZoomOut: IconType;
