import express from "express";
import cors from "cors";
import dotenv from "dotenv";

import authRoute from "./routes/authRouter.js";
import userRoute from "./routes/userRouter.js";
import taskRoute from "./routes/taskRouter.js";
import reportRoute from "./routes/reportRouter.js";

import connectToDB from "./config/db.js";

dotenv.config();

const app = express();

//Middleware to handel cores
app.use(
  cors({
    origin: process.env.CLIENT_URL || "*",
    methods: ["GET", "POST", "PUT", "DELETE"],
    allowedHeaders: ["Content-Type", "Authorization"],
    credentials: true,
  })
);

//connecting to database
connectToDB();

//Middleware
app.use(express.json());

//Routes
app.use("/api/auth", authRoute);
app.use("/api/users", userRoute);
app.use("/api/tasks", taskRoute);
app.use("/api/reports", reportRoute);

//Server upload folder
app.use("/uploads",express.static(path.join(__dirname,"uploads")));

//Start Server
const PORT = process.env.PORT || 8000
app.listen(PORT, () => console.log(`Server is running on PORT: ${PORT}`));
