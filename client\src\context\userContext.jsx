import React, { createContext, useState, useEffect } from "react";
import axiosInstance from "../utils/axiosInstance";
import { API_PATHS } from "../utils/apiPath";

export const UserContext = createContext();

const UserProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loding, setLoding] = useState(null);

  useEffect(() => {
    if (user) return;

    const accessToken = localStorage.getItem("token");
    if (!accessToken) {
      setLoding(false);
      return;
    }

    const fetchUsers = async () => {
      try {
        const response = await axiosInstance.get(API_PATHS.AUTH.GET_PROFILE);
        setUser(response.data);
      } catch (error) {
        console.error("User not Authenticated", error);
        clearUser();
      } finally {
        setLoding(false);
      }
    };
    fetchUsers();
  }, []);

  const updateUesr = (userData) => {
    setUser(userData);
    localStorage.setItem("token", userData, token); //save token
    setLoding(false);
  };

  const clearUser = () => {
    setUser(null);
    localStorage.removeItem("token");
  };

  return (
    <UserContext.Provider value={{user,loding,updateUesr,clearUser}}>
        {children}
    </UserContext.Provider>
  );
};

export default UserProvider