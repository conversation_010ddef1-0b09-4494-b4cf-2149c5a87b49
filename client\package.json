{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.12", "axios": "^1.11.0", "moment": "^2.30.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hot-toast": "^2.6.0", "react-icon": "^1.0.0", "react-icons": "^5.5.0", "react-router-dom": "^7.8.2", "recharts": "^3.1.2", "tailwindcss": "^4.1.12"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.2"}}