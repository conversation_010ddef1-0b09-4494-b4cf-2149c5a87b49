import axios from "axios";
import { BASE_URL } from "./apiPath";

const axiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "appliaction/json",
    Accept: "appliaction/json",
  },
});

//Request Intercepter
axiosInstance.interceptors.request.use(
  (config) => {
    const accessToken = localStorage.getItem("token");
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

//Response Intercepter
axiosInstance.interceptors.request.use(
  (response) => {
    return response;
  },
  (error) => {
    //Handle common errors globaly
    if (error.response) {
      if (error.response.status === 401) {
        //Rediect to login page
        window.location.href = "/login";
      } else if (error.response.status === 500) {
        console.error("Server error.Please try again later.")
      }
    }else if(error.code==="ECONNADORTED"){
        console.error("Request timeout.Please try again .")
    }
    return Promise.reject(error)
  }
);

export default axiosInstance